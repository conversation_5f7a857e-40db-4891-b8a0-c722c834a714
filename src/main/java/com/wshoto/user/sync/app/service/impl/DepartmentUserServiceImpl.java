package com.wshoto.user.sync.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.dao.entity.Department;
import com.wshoto.user.sync.app.dao.entity.DepartmentUser;
import com.wshoto.user.sync.app.dao.mapper.DepartmentUserMapper;
import com.wshoto.user.sync.app.service.DepartmentUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 部门成员关系数据 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
@Slf4j
@Service
public class DepartmentUserServiceImpl extends ServiceImpl<DepartmentUserMapper, DepartmentUser>
        implements DepartmentUserService {

    @Resource
    private DepartmentUserMapper departmentUserMapper;

    @Override
    public void updateTargetDeptId(String tenantId, String sourceDeptId, String targetDeptId) {
        departmentUserMapper.update(new LambdaUpdateWrapper<DepartmentUser>().eq(DepartmentUser::getTenantId, tenantId)
                                                                             .eq(DepartmentUser::getDeleted,
                                                                                 JobConstant.DELETED_NO)
                                                                             .eq(DepartmentUser::getSourceDeptId,
                                                                                 sourceDeptId)
                                                                             .isNull(DepartmentUser::getTargetDeptId)
                                                                             .set(DepartmentUser::getTargetDeptId,
                                                                                  targetDeptId)
                                                                             .set(DepartmentUser::getGmtModified,
                                                                                  new Date()));
        log.info("updated_target_dept_id: {}: {}", sourceDeptId, targetDeptId);
    }

    @Override
    public void deleteDepartmentUser(Department department) {
        departmentUserMapper.update(
                new LambdaUpdateWrapper<DepartmentUser>().eq(DepartmentUser::getTenantId, department.getTenantId())
                                                         .eq(DepartmentUser::getDeleted, JobConstant.DELETED_NO)
                                                         .eq(DepartmentUser::getSourceDeptId,
                                                             department.getSourceDeptId())
                                                         .set(DepartmentUser::getDeleted, JobConstant.DELETED_YES)
                                                         .set(DepartmentUser::getGmtModified, new Date()));
        log.info("deleted_department_user: {}:{}", department.getSourceDeptId(), department.getTargetDeptId());
    }

}
