package com.wshoto.user.sync.app.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 同步日志
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("us_sync_log")
public class SyncLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 接口地址
     */
    @TableField("url")
    private String url;

    /**
     * 参数
     */
    @TableField("request")
    private String request;

    /**
     * 返回结果
     */
    @TableField("result")
    private String result;

    /**
     * 接口状态
     */
    @TableField("status")
    private String status;

    /**
     * 批次号
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 当前版本
     */
    @TableField("version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private Date gmtModified;


}
