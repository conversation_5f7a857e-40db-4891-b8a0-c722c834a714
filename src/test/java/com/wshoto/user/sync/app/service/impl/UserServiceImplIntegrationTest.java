package com.wshoto.user.sync.app.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wshoto.user.sync.app.BaseIntegrationTest;
import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.common.enums.SyncStatusEnum;
import com.wshoto.user.sync.app.component.QwSyncComponent;
import com.wshoto.user.sync.app.dao.entity.User;
import com.wshoto.user.sync.wx.cp.config.WxCpProperties;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

/**
 * UserServiceImpl测试类
 *
 * <AUTHOR>
 * @date 2024-7-16
 */
@TestMethodOrder(MethodOrderer.MethodName.class)
class UserServiceImplIntegrationTest extends BaseIntegrationTest {

    private static final String SOURCE_USER_ID_TEST = "sourceUseridTestUserServiceImpl";

    @Autowired
    WxCpProperties wxCpProperties;

    @Autowired
    UserServiceImpl userService;

    @Autowired
    DepartmentUserServiceImpl departmentUserService;

    @MockBean
    QwSyncComponent qwSyncComponent;

    @Test
    void _01_syncQw_success() {
        Mockito.doNothing()
               .when(qwSyncComponent)
               .createUser(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(),
                           ArgumentMatchers.any(), ArgumentMatchers.any());
        Mockito.doNothing()
               .when(qwSyncComponent)
               .updateUser(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(),
                           ArgumentMatchers.any(), ArgumentMatchers.any());
        Mockito.doNothing()
               .when(qwSyncComponent)
               .deleteUser(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());
        String tenantId = wxCpProperties.getAppConfigs()
                                        .get(0)
                                        .getCorpId();

        // 执行待验证部分,验证执行结果
        Assertions.assertDoesNotThrow(
                () -> userService.syncUserToWeCom(tenantId, userService.list(), departmentUserService.list(),
                                                  "batchNoTest"));

    }

    @Test
    void _02_updateSyncStatusProgressing_success() {
        String tenantId = wxCpProperties.getAppConfigs()
                                        .get(0)
                                        .getCorpId();

        User userNew = new User();
        userNew.setSourceUserid(SOURCE_USER_ID_TEST);
        userNew.setTargetUserid("targetUseridTest");
        userNew.setTenantId(tenantId);
        userNew.setDeleted(JobConstant.DELETED_NO);
        userNew.setSyncStatus(SyncStatusEnum.INIT.getCode());
        userService.save(userNew);

        // 执行待验证部分
        userService.updateSyncStatusProgressing(userNew);

        // 验证执行结果
        User userActual = userService.getById(userNew.getId());
        Assertions.assertEquals(SyncStatusEnum.PROGRESSING.getCode(), userActual.getSyncStatus());
    }

    @Test
    void _03_updateSyncStatusSuccess_success() {
        User userToBeUpdated =
                userService.getOne(new LambdaQueryWrapper<User>().eq(User::getSourceUserid, SOURCE_USER_ID_TEST));

        // 执行待验证部分
        userService.updateSyncStatusSuccess(userToBeUpdated);

        // 验证执行结果
        User userActual = userService.getById(userToBeUpdated.getId());
        Assertions.assertEquals(SyncStatusEnum.SUCCESS.getCode(), userActual.getSyncStatus());
    }

    @Test
    void _04_updateSyncStatusSuccess_success() {
        User userToBeUpdated =
                userService.getOne(new LambdaQueryWrapper<User>().eq(User::getSourceUserid, SOURCE_USER_ID_TEST));

        // 执行待验证部分
        userService.updateSyncStatusFail(userToBeUpdated);

        // 验证执行结果
        User userActual = userService.getById(userToBeUpdated.getId());
        Assertions.assertEquals(SyncStatusEnum.FAIL.getCode(), userActual.getSyncStatus());
    }

    @Test
    void _05_deleteUserSuccess_success() {
        User userToBeUpdated =
                userService.getOne(new LambdaQueryWrapper<User>().eq(User::getSourceUserid, SOURCE_USER_ID_TEST));

        // 执行待验证部分
        userService.deleteUserSuccess(userToBeUpdated);

        // 验证执行结果
        User userActual = userService.getById(userToBeUpdated.getId());
        Assertions.assertEquals(JobConstant.DELETED_YES, userActual.getDeleted());
        Assertions.assertEquals(SyncStatusEnum.SUCCESS.getCode(), userActual.getSyncStatus());
    }

}
