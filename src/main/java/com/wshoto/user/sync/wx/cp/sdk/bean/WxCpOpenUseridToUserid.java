package com.wshoto.user.sync.wx.cp.sdk.bean;

import com.google.gson.annotations.SerializedName;
import com.wshoto.user.sync.wx.cp.sdk.util.json.WxCpGsonHolder;
import lombok.Data;

import java.io.Serializable;

/**
 * userid转换
 * 将代开发应用或第三方应用获取的密文open_userid转换为明文userid
 * 中间对象
 * <AUTHOR>
 */
@Data
public class WxCpOpenUseridToUserid implements Serializable {
  private static final long serialVersionUID = 1714909184316350423L;

  @Override
  public String toString() {
    return WxCpGsonHolder.get().toJson(this);
  }

  /**
   * From json wx cp open userid to userid result.
   *
   * @param json the json
   * @return the wx cp open userid to userid result.
   */
  public static WxCpOpenUseridToUserid fromJson(String json) {
    return WxCpGsonHolder.get().from<PERSON>son(json, WxCpOpenUseridToUserid.class);
  }

  @SerializedName("userid")
  private String userid;

  @SerializedName("open_userid")
  private String openUserid;

}
