# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
applicationSession.session.ise=invalid session state
applicationSession.value.iae=null value
fileStore.saving=Saving Session {0} to file {1}
fileStore.loading=Loading Session {0} from file {1}
fileStore.removing=Removing Session {0} at file {1}
fileStore.deleteFailed=Unable to delete file [{0}] which is preventing the creation of the session storage location
fileStore.createFailed=Unable to create directory [{0}] for the storage of session data
JDBCStore.close=Exception closing database connection {0}
JDBCStore.saving=Saving Session {0} to database {1}
JDBCStore.loading=Loading Session {0} from database {1}
JDBCStore.removing=Removing Session {0} at database {1}
JDBCStore.SQLException=SQL Error {0}
serverSession.value.iae=null value
sessionManagerImpl.createRandom=Created random number generator for session ID generation in {0}ms.
sessionManagerImpl.createSession.tmase=createSession: Too many active sessions
sessionManagerImpl.sessionTimeout=Invalid session timeout setting {0}
sessionManagerImpl.getSession.ise=getSession: Session id cannot be null
sessionManagerImpl.expireException=processsExpire:  Exception during session expiration
sessionManagerImpl.loading=Loading persisted sessions from {0}
sessionManagerImpl.loading.cnfe=ClassNotFoundException while loading persisted sessions: {0}
sessionManagerImpl.loading.ioe=IOException while loading persisted sessions: {0}
sessionManagerImpl.unloading=Saving persisted sessions to {0}
sessionManagerImpl.unloading.debug=Unloading persisted sessions
sessionManagerImpl.unloading.ioe=IOException while saving persisted sessions: {0}
sessionManagerImpl.unloading.nosessions=No persisted sessions to unload
sessionManagerImpl.managerLoad=Exception loading sessions from persistent storage
sessionManagerImpl.managerUnload=Exception unloading sessions to persistent storage
sessionManagerImpl.createSession.ise=createSession: Session id cannot be null
sessionImpl.attributeEvent=Session attribute event listener threw exception
sessionImpl.bindingEvent=Session binding event listener threw exception
sessionImpl.invalidate.ise=invalidate: Session already invalidated
sessionImpl.isNew.ise=isNew: Session already invalidated
sessionImpl.getAttribute.ise=getAttribute: Session already invalidated
sessionImpl.getAttributeNames.ise=getAttributeNames: Session already invalidated
sessionImpl.getCreationTime.ise=getCreationTime: Session already invalidated
sessionImpl.getThisAccessedTime.ise=getThisAccessedTime: Session already invalidated
sessionImpl.getLastAccessedTime.ise=getLastAccessedTime: Session already invalidated
sessionImpl.getId.ise=getId: Session already invalidated
sessionImpl.getMaxInactiveInterval.ise=getMaxInactiveInterval: Session already invalidated
sessionImpl.getValueNames.ise=getValueNames: Session already invalidated
sessionImpl.logoutfail=Exception logging out user when expiring session
sessionImpl.notSerializable=Cannot serialize session attribute {0} for session {1}
sessionImpl.removeAttribute.ise=removeAttribute: Session already invalidated
sessionImpl.sessionEvent=Session event listener threw exception
sessionImpl.setAttribute.iae=setAttribute: Non-serializable attribute {0}
sessionImpl.setAttribute.ise=setAttribute: Session [{0}] has already been invalidated
sessionImpl.setAttribute.namenull=setAttribute: name parameter cannot be null
sessionImpl.sessionCreated=Created Session id = {0}
persistentManager.loading=Loading {0} persisted sessions
persistentManager.unloading=Saving {0} persisted sessions
persistentManager.expiring=Expiring {0} sessions before saving them
persistentManager.deserializeError=Error deserializing Session {0}: {1}
persistentManager.serializeError=Error serializing Session {0}: {1}
persistentManager.swapMaxIdle=Swapping session {0} to Store, idle for {1} seconds
persistentManager.backupMaxIdle=Backing up session {0} to Store, idle for {1} seconds
persistentManager.backupException=Exception occurred when backing up Session {0}: {1}
persistentManager.tooManyActive=Too many active sessions, {0}, looking for idle sessions to swap out
persistentManager.swapTooManyActive=Swapping out session {0}, idle for {1} seconds too many sessions active
persistentManager.processSwaps=Checking for sessions to swap out, {0} active sessions in memory
persistentManager.activeSession=Session {0} has been idle for {1} seconds
persistentManager.swapIn=Swapping session {0} in from Store
persistentManager.swapInException=Exception in the Store during swapIn: {0}
persistentManager.swapInInvalid=Swapped session {0} is invalid
persistentManager.storeKeysException=Unable to determine the list of session IDs for sessions in the session store, assuming that the store is empty
persistentManager.storeSizeException=Unable to determine the number of sessions in the session store, assuming that the store is empty
