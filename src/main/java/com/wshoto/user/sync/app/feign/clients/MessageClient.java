package com.wshoto.user.sync.app.feign.clients;

import com.wshoto.user.sync.app.feign.entity.EmailReplyVo;
import com.wshoto.user.sync.app.feign.entity.EmailSendCommandVo;
import com.wshoto.user.sync.app.feign.entity.RespWrapVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * unified-messaging-service FeignClient
 *
 * <AUTHOR>
 * @date 2024-7-24
 */
@FeignClient(name = "unifiedMessagingService", url = "${application.services.unifiedMessaging}")
public interface MessageClient {

    @PostMapping(value = "/email/alicloud/send")
    RespWrapVo<EmailReplyVo> sendWithReply(EmailSendCommandVo command);

}
