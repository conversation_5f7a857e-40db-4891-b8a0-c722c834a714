package com.wshoto.user.sync.app.service.impl;

import com.wshoto.user.sync.app.dao.entity.SyncLog;
import com.wshoto.user.sync.app.dao.mapper.SyncLogMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class SyncLogServiceImplTest {

    @Mock
    private SyncLogMapper syncLogMapper;

    @InjectMocks
    private SyncLogServiceImpl syncLogService;

    @Test
    @DisplayName("saveLog-正常入参-成功保存")
    void saveLog_withValidInput_shouldSaveLogSuccessfully() {
        // Arrange
        String tenantId = "tenant123";
        String url = "http://example.com/api";
        String request = "{\"key\":\"value\"}";
        String response = "{\"status\":\"success\"}";
        int code = 200;
        String batchNo = "batch123";

        // Act
        syncLogService.saveLog(tenantId, url, request, response, code, batchNo);

        // Assert
        verify(syncLogMapper).insert(any(SyncLog.class));
    }

}