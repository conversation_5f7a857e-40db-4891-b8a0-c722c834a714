package com.wshoto.user.sync.app.job;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.wshoto.user.sync.app.common.enums.DataStatusEnum;
import com.wshoto.user.sync.app.common.enums.SyncStatusEnum;
import com.wshoto.user.sync.app.dao.entity.DepartmentUser;
import com.wshoto.user.sync.app.dao.entity.User;
import com.wshoto.user.sync.app.dao.entity.UserLoggedDTO;
import com.wshoto.user.sync.app.service.DepartmentUserService;
import com.wshoto.user.sync.app.service.UserLoggedService;
import com.wshoto.user.sync.app.service.UserService;
import org.instancio.Instancio;
import org.instancio.Select;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * Direct tests for the syncStuff method in QwUserSyncJob
 * Following AAA pattern and enterprise coding standards
 */
@ExtendWith(MockitoExtension.class)
class DirectSyncStuffTest {

    private QwUserSyncJob qwUserSyncJob;

    @Mock
    private UserService userService;

    @Mock
    private UserLoggedService userLoggedService;

    @Mock
    private DepartmentUserService departmentUserService;
    
    @Captor
    private ArgumentCaptor<List<User>> userListCaptor;

    @BeforeEach
    void setUp() {
        qwUserSyncJob = new QwUserSyncJob();
        ReflectionTestUtils.setField(qwUserSyncJob, "userService", userService);
        ReflectionTestUtils.setField(qwUserSyncJob, "userLoggedService", userLoggedService);
        ReflectionTestUtils.setField(qwUserSyncJob, "departmentUserService", departmentUserService);
        
        // Set up common lenient stubs to avoid UnnecessaryStubbingException
        lenient().doReturn(Collections.emptyList()).when(departmentUserService).list((Wrapper<DepartmentUser>) any());
    }

    /**
     * Test case: No users to sync
     * Expected behavior: No sync operation performed
     */
    @Test
    @DisplayName("无用户需要同步时不执行用户同步")
    void whenNoUsers_thenNoSyncIsPerformed() {
        // Arrange
        var tenantId = "TENANT_ID";
        var batchNo = "BATCH_NO";
        
        // Mock the service calls - explicitly specify the method to avoid ambiguity
        doReturn(Collections.emptyList()).when(userService).list((Wrapper<User>) any());

        // Act
        qwUserSyncJob.syncStuff(tenantId, batchNo);

        // Assert
        verify(userLoggedService, never()).list();
        verify(userService, never()).syncUserToWeCom(any(), anyList(), anyList(), any());
    }

    /**
     * Test case: All users are already logged in
     * Expected behavior: No sync operation performed
     */
    @Test
    @DisplayName("所有用户已登录时不执行用户同步")
    void whenAllUsersLoggedIn_thenNoSyncIsPerformed() {
        // Arrange
        var tenantId = "TENANT_ID";
        var batchNo = "BATCH_NO";
        
        // Create users to sync
        var user = Instancio.of(User.class)
                .set(Select.field(User::getAadId), "AAD_ID_1")
                .set(Select.field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                .set(Select.field(User::getDataStatus), DataStatusEnum.CREATE.getCode())
                .create();
                
        // Create logged in users with same AAD IDs
        var loggedUser = Instancio.of(UserLoggedDTO.class)
                .set(Select.field(UserLoggedDTO::getAadId), "AAD_ID_1")
                .set(Select.field(UserLoggedDTO::getSourceUserid), "EMAIL_1")
                .create();
        
        // Mock the service calls - explicitly specify the method to avoid ambiguity
        doReturn(List.of(user)).when(userService).list((Wrapper<User>) any());
        doReturn(List.of(loggedUser)).when(userLoggedService).list();

        // Act
        qwUserSyncJob.syncStuff(tenantId, batchNo);

        // Assert
        verify(userService, never()).syncUserToWeCom(any(), anyList(), anyList(), any());
    }

    /**
     * Test case: Some users are logged in, some are not
     * Expected behavior: Only non-logged in users are synced
     */
    @Test
    @DisplayName("部分用户已登录时仅同步未登录用户")
    void whenSomeUsersLoggedIn_thenOnlySyncNonLoggedUsers() {
        // Arrange
        var tenantId = "TENANT_ID";
        var batchNo = "BATCH_NO";
        
        // Create users to sync - one logged in, one not logged in
        var user1 = Instancio.of(User.class)
                .set(Select.field(User::getAadId), "AAD_ID_1")
                .set(Select.field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                .set(Select.field(User::getDataStatus), DataStatusEnum.CREATE.getCode())
                .create();
        var user2 = Instancio.of(User.class)
                .set(Select.field(User::getAadId), "AAD_ID_2")
                .set(Select.field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                .set(Select.field(User::getDataStatus), DataStatusEnum.CREATE.getCode())
                .create();
                
        // Only AAD_ID_1 is logged in
        var loggedUser = Instancio.of(UserLoggedDTO.class)
                .set(Select.field(UserLoggedDTO::getAadId), "AAD_ID_1")
                .set(Select.field(UserLoggedDTO::getSourceUserid), "EMAIL_1")
                .create();
        
        // Mock the service calls - explicitly specify the method to avoid ambiguity
        doReturn(List.of(user1, user2)).when(userService).list((Wrapper<User>) any());
        doReturn(List.of(loggedUser)).when(userLoggedService).list();

        // Act
        qwUserSyncJob.syncStuff(tenantId, batchNo);

        // Assert
        // Capture and verify the user list passed to syncUserToWeCom
        verify(userService).syncUserToWeCom(
                eq(tenantId),
                userListCaptor.capture(),
                anyList(),
                eq(batchNo)
        );
        
        var capturedUsers = userListCaptor.getValue();
        assertThat(capturedUsers).hasSize(1);
        
        // Find the user with AAD_ID_2 in the captured list
        Optional<User> capturedUser = capturedUsers.stream()
                .filter(u -> "AAD_ID_2".equals(u.getAadId()))
                .findFirst();
                
        assertThat(capturedUser).isPresent()
                .hasValueSatisfying(user -> 
                        assertThat(user.getAadId()).isEqualTo("AAD_ID_2"));
    }

    /**
     * Test case: Large number of users requiring batch processing
     * Expected behavior: Users are processed in batches
     */
    @Test
    @DisplayName("大量用户时正确处理分批")
    void whenManyUsers_thenProcessesInBatches() {
        // Arrange
        var tenantId = "TENANT_ID";
        var batchNo = "BATCH_NO";
        
        // Create a large list of users that will be partitioned (3 batches with max size 1000)
        var users = Instancio.ofList(User.class)
                .size(2500)
                .create();
        
        // Mock the service calls - explicitly specify the method to avoid ambiguity
        doReturn(users).when(userService).list((Wrapper<User>) any());
        doReturn(Collections.emptyList()).when(userLoggedService).list();

        // Act
        qwUserSyncJob.syncStuff(tenantId, batchNo);

        // Assert
        // Verify that userService.syncUserToWeCom is called 3 times (once for each batch)
        verify(userService, times(3)).syncUserToWeCom(
                eq(tenantId),
                anyList(),
                anyList(),
                eq(batchNo)
        );
    }

    /**
     * Test case: Users with direct leaders
     * Expected behavior: Leader references are updated to email addresses
     */
    @Test
    @DisplayName("用户有直接领导时更新领导引用")
    void whenUsersHaveDirectLeaders_thenUpdatesLeaderReferences() {
        // Arrange
        var tenantId = "TENANT_ID";
        var batchNo = "BATCH_NO";
        
        // Create users with direct leaders
        var user1 = Instancio.of(User.class)
                .set(Select.field(User::getAadId), "USER_AAD_1")
                .set(Select.field(User::getDirectLeader), "LEADER_AAD_1")
                .set(Select.field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                .set(Select.field(User::getDataStatus), DataStatusEnum.CREATE.getCode())
                .create();
                
        var user2 = Instancio.of(User.class)
                .set(Select.field(User::getAadId), "USER_AAD_2")
                .set(Select.field(User::getDirectLeader), "LEADER_AAD_2")
                .set(Select.field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                .set(Select.field(User::getDataStatus), DataStatusEnum.CREATE.getCode())
                .create();
                
        // Leaders are logged in
        var leader1 = Instancio.of(UserLoggedDTO.class)
                .set(Select.field(UserLoggedDTO::getAadId), "LEADER_AAD_1")
                .set(Select.field(UserLoggedDTO::getSourceUserid), "LEADER_EMAIL_1")
                .create();
                
        var leader2 = Instancio.of(UserLoggedDTO.class)
                .set(Select.field(UserLoggedDTO::getAadId), "LEADER_AAD_2")
                .set(Select.field(UserLoggedDTO::getSourceUserid), "LEADER_EMAIL_2")
                .create();
        
        // Mock the service calls - explicitly specify the method to avoid ambiguity
        doReturn(List.of(user1, user2)).when(userService).list((Wrapper<User>) any());
        doReturn(List.of(leader1, leader2)).when(userLoggedService).list();

        // Act
        qwUserSyncJob.syncStuff(tenantId, batchNo);

        // Assert
        // Capture the user list passed to syncUserToWeCom
        verify(userService).syncUserToWeCom(
                eq(tenantId),
                userListCaptor.capture(),
                anyList(),
                eq(batchNo)
        );
        
        // Verify that the direct leader values have been updated to email addresses
        var capturedUsers = userListCaptor.getValue();
        assertThat(capturedUsers).hasSize(2);
        
        // Find users by AAD ID
        Optional<User> updatedUser1 = capturedUsers.stream()
                .filter(u -> "USER_AAD_1".equals(u.getAadId()))
                .findFirst();
                
        Optional<User> updatedUser2 = capturedUsers.stream()
                .filter(u -> "USER_AAD_2".equals(u.getAadId()))
                .findFirst();
        
        // Verify that both users are present and their leader references are updated
        assertThat(updatedUser1).isPresent()
                .hasValueSatisfying(user -> 
                        assertThat(user.getDirectLeader()).isEqualTo("LEADER_EMAIL_1"));
                        
        assertThat(updatedUser2).isPresent()
                .hasValueSatisfying(user -> 
                        assertThat(user.getDirectLeader()).isEqualTo("LEADER_EMAIL_2"));
    }

    /**
     * Test case: Users with different enable status
     * Expected behavior: Users who are not logged in OR are disabled will be synced
     */
    @Test
    @DisplayName("同步未登录或禁用状态的用户")
    void whenUsersHaveDifferentEnableStatus_thenSyncNotLoggedInOrDisabledUsers() {
        // Arrange
        var tenantId = "TENANT_ID";
        var batchNo = "BATCH_NO";
        
        // Create users with different enable status
        var enabledUser = Instancio.of(User.class)
                .set(Select.field(User::getAadId), "ENABLED_USER_AAD")
                .set(Select.field(User::getEnable), 1)
                .set(Select.field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                .set(Select.field(User::getDataStatus), DataStatusEnum.CREATE.getCode())
                .create();
                
        var disabledUser = Instancio.of(User.class)
                .set(Select.field(User::getAadId), "DISABLED_USER_AAD")
                .set(Select.field(User::getEnable), 0)
                .set(Select.field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                .set(Select.field(User::getDataStatus), DataStatusEnum.CREATE.getCode())
                .create();
                
        var loggedInEnabledUser = Instancio.of(User.class)
                .set(Select.field(User::getAadId), "LOGGED_IN_ENABLED_AAD")
                .set(Select.field(User::getEnable), 1)
                .set(Select.field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                .set(Select.field(User::getDataStatus), DataStatusEnum.CREATE.getCode())
                .create();
                
        var loggedInDisabledUser = Instancio.of(User.class)
                .set(Select.field(User::getAadId), "LOGGED_IN_DISABLED_AAD")
                .set(Select.field(User::getSourceUserid), "LOGGED_IN_DISABLED_EMAIL")
                .set(Select.field(User::getEnable), 0)
                .set(Select.field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                .set(Select.field(User::getDataStatus), DataStatusEnum.CREATE.getCode())
                .create();
        
        // Create logged in users
        var loggedInEnabled = Instancio.of(UserLoggedDTO.class)
                .set(Select.field(UserLoggedDTO::getAadId), "LOGGED_IN_ENABLED_AAD")
                .set(Select.field(UserLoggedDTO::getSourceUserid), "LOGGED_IN_ENABLED_EMAIL")
                .create();
                
        var loggedInDisabled = Instancio.of(UserLoggedDTO.class)
                .set(Select.field(UserLoggedDTO::getAadId), "LOGGED_IN_DISABLED_AAD")
                .set(Select.field(UserLoggedDTO::getSourceUserid), "LOGGED_IN_DISABLED_EMAIL")
                .create();
        
        // Mock the service calls
        doReturn(List.of(enabledUser, disabledUser, loggedInEnabledUser, loggedInDisabledUser))
                .when(userService).list((Wrapper<User>) any());
        doReturn(List.of(loggedInEnabled, loggedInDisabled))
                .when(userLoggedService).list();

        // Act
        qwUserSyncJob.syncStuff(tenantId, batchNo);

        // Assert
        // Capture the user list passed to syncUserToWeCom
        verify(userService).syncUserToWeCom(
                eq(tenantId),
                userListCaptor.capture(),
                anyList(),
                eq(batchNo)
        );
        
        // Verify that the correct users are included
        var capturedUsers = userListCaptor.getValue();
        assertThat(capturedUsers).hasSize(3);
        
        // Verify that not logged in users (both enabled and disabled) are included
        boolean containsEnabledUser = capturedUsers.stream()
                .anyMatch(u -> "ENABLED_USER_AAD".equals(u.getAadId()));
        boolean containsDisabledUser = capturedUsers.stream()
                .anyMatch(u -> "DISABLED_USER_AAD".equals(u.getAadId()));
        
        assertThat(containsEnabledUser).isTrue();
        assertThat(containsDisabledUser).isTrue();
        
        // Verify that logged in but disabled user is included
        // Note: For disabled logged-in users, the AAD ID is changed to the source user ID
        boolean containsLoggedInDisabledUser = capturedUsers.stream()
                .anyMatch(u -> "LOGGED_IN_DISABLED_EMAIL".equals(u.getAadId()));
        assertThat(containsLoggedInDisabledUser).isTrue();
        
        // Verify that logged in and enabled user is NOT included
        boolean containsLoggedInEnabledUser = capturedUsers.stream()
                .anyMatch(u -> "LOGGED_IN_ENABLED_AAD".equals(u.getAadId()));
        assertThat(containsLoggedInEnabledUser).isFalse();
    }

    /**
     * Test case: Selective user synchronization by AAD IDs
     * Expected behavior: Only users with the specified AAD IDs are synced
     */
    @Test
    @DisplayName("指定AAD ID同步时仅同步指定用户")
    void whenSpecificAadIdsProvided_thenOnlySyncSpecifiedUsers() {
        // Arrange
        var tenantId = "TENANT_ID";
        var batchNo = "BATCH_NO";
        
        // Create test users with different AAD IDs
        var user1 = Instancio.of(User.class)
                .set(Select.field(User::getAadId), "AAD_ID_1")
                .set(Select.field(User::getTenantId), tenantId)
                .set(Select.field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                .set(Select.field(User::getDataStatus), DataStatusEnum.CREATE.getCode())
                .create();
        
        var user3 = Instancio.of(User.class)
                .set(Select.field(User::getAadId), "AAD_ID_3")
                .set(Select.field(User::getTenantId), tenantId)
                .set(Select.field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                .set(Select.field(User::getDataStatus), DataStatusEnum.CREATE.getCode())
                .create();
        
        // Create a list of specific AAD IDs to sync (only AAD_ID_1 and AAD_ID_3)
        var specificAadIds = List.of("AAD_ID_1", "AAD_ID_3");
        
        // Mock the userService.list() to return only users with the specified AAD IDs
        // This simulates the filtering done by the in(User::getAadId, specificAadIds) condition
        doReturn(List.of(user1, user3)).when(userService).list((Wrapper<User>) any());
        
        // Mock empty logged users to ensure all users are processed
        doReturn(Collections.emptyList()).when(userLoggedService).list();
        
        // Act
        qwUserSyncJob.syncStuff(tenantId, batchNo, specificAadIds);
        
        // Assert
        // Capture the user list passed to syncUserToWeCom
        verify(userService).syncUserToWeCom(
                eq(tenantId),
                userListCaptor.capture(),
                anyList(),
                eq(batchNo)
        );
        
        // Verify that only the specified users are included in the sync
        var capturedUsers = userListCaptor.getValue();
        assertThat(capturedUsers).hasSize(2);
        
        // Verify that only users with AAD_ID_1 and AAD_ID_3 are included
        var capturedAadIds = capturedUsers.stream()
                .map(User::getAadId)
                .collect(Collectors.toSet());
        
        assertThat(capturedAadIds)
                .containsExactlyInAnyOrder("AAD_ID_1", "AAD_ID_3")
                .doesNotContain("AAD_ID_2");
    }
}
