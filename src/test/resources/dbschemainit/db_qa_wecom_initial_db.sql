drop table if exists "us_user";
create table "us_user"
(
    "id"                int8         not null,
    "source_userid"     varchar(64)  not null,
    "name"              varchar(64),
    "alias"             varchar(64),
    "mobile"            varchar(20),
    "position"          varchar(128),
    "gender"            varchar(20),
    "biz_mail"          varchar(64),
    "telephone"         varchar(32),
    "direct_leader"     varchar(64),
    "avatar_mediaid"    varchar(50),
    "enable"            int2,
    "extattr"           varchar(255),
    "to_invite"         int2                  default 1,
    "external_position" varchar(12),
    "external_profile"  varchar(255),
    "address"           varchar(128),
    "main_department"   int4,
    "data_status"       int4,
    "sync_status"       int4                  default 0,
    "version"           int4         not null default 0,
    "deleted"           int2         not null default 0,
    "gmt_create"        timestamp(6) not null default current_timestamp,
    "gmt_modified"      timestamp(6)          default current_timestamp,
    "target_userid"     varchar(64)  not null,
    "tenant_id"         varchar(50),
    "email"             varchar(64),
    aad_id              varchar(36)
)
;
alter table "us_user"
    add constraint "us_user_pk" primary key ("id");


drop table if exists "us_department";
create table "us_department"
(
    "id"                    int8         not null,
    "source_dept_id"        varchar(200),
    "source_dept_parent_id" varchar(200),
    "target_dept_id"        varchar(50),
    "target_dept_parent_id" varchar(50),
    "name"                  varchar(100) not null,
    "name_en"               varchar(100),
    "order"                 int4                  default 0,
    "data_status"           int4,
    "sync_status"           int4                  default 0,
    "version"               int4         not null default 0,
    "deleted"               int2         not null default 0,
    "gmt_create"            timestamp(6) not null default current_timestamp,
    "gmt_modified"          timestamp(6)          default current_timestamp,
    "tenant_id"             varchar(50)
)
;
alter table "us_department"
    add constraint "us_department_pk" primary key ("id");


drop table if exists "us_department_user";
create table "us_department_user"
(
    "id"             int8         not null,
    "source_dept_id" varchar(100),
    "target_dept_id" varchar(100),
    "tartget_userid" varchar(64),
    "is_dept_leader" int2,
    "dept_order"     int4,
    "version"        int4         not null default 0,
    "deleted"        int2         not null default 0,
    "gmt_create"     timestamp(6) not null default current_timestamp,
    "gmt_modified"   timestamp(6)          default current_timestamp,
    "tenant_id"      varchar(50),
    aad_id           varchar(36)
)
;
alter table "us_department_user"
    add constraint "us_department_user_pk" primary key ("id");


drop table if exists "us_notify_config";
create table "us_notify_config"
(
    "id"           int8         not null,
    "notify_type"  int4,
    "is_open"      int2,
    "mail"         varchar(255),
    "username"     varchar(255),
    "password"     varchar(255),
    "host"         varchar(255),
    "port"         int8         not null,
    "robot_key"    varchar(200),
    "version"      int4         not null default 0,
    "deleted"      int2         not null default 0,
    "gmt_create"   timestamp(6) not null default current_timestamp,
    "gmt_modified" timestamp(6)          default current_timestamp,
    "to_user"      varchar(255),
    "tenant_id"    varchar(50),
    "ssl_enable"   bool         not null default false,
    "time_out"     int4
)
;
alter table "us_notify_config"
    add constraint "us_notify_config_pk" primary key ("id");


drop table if exists "us_job";
create table "us_job"
(
    "id"           int8         not null,
    "job_id"       varchar(100),
    "tenant_id"    varchar(50),
    "cron"         varchar(50),
    "remark"       varchar(200),
    "version"      int4         not null default 0,
    "deleted"      int2         not null default 0,
    "gmt_create"   timestamp(6) not null default current_timestamp,
    "gmt_modified" timestamp(6)          default current_timestamp
)
;


drop table if exists "us_sync_log";
create table "us_sync_log"
(
    "id"           int8         not null,
    "url"          varchar(100),
    "request"      varchar(255),
    "result"       varchar(255),
    "status"       varchar(50),
    "version"      int4         not null default 0,
    "deleted"      int2         not null default 0,
    "gmt_create"   timestamp(6) not null default current_timestamp,
    "gmt_modified" timestamp(6)          default current_timestamp,
    "tenant_id"    varchar(50),
    "batch_no"     varchar(50)
)
;


drop table if exists "us_notify_config";
create table "us_notify_config"
(
    "id"           int8         not null,
    "notify_type"  int4,
    "is_open"      int2,
    "mail"         varchar(255) not null,
    "username"     varchar(255) not null,
    "password"     varchar(255) not null,
    "host"         varchar(255) not null,
    "port"         int8         not null,
    "robot_key"    varchar(200),
    "version"      int4         not null default 0,
    "deleted"      int2         not null default 0,
    "gmt_create"   timestamp(6) not null default current_timestamp,
    "gmt_modified" timestamp(6)          default current_timestamp,
    "to_user"      varchar(255),
    "tenant_id"    varchar(50),
    "ssl_enable"   bool         not null default false,
    "time_out"     int4
)
;
alter table "us_notify_config"
    add constraint "us_notify_config_pk" primary key ("id");


drop table if exists "us_notify_config_template";
create table "us_notify_config_template"
(
    "id"           int8           not null,
    "name"         varchar(64)    not null,
    "code"         varchar(64)    not null,
    "config_id"    int8           not null,
    "nickname"     varchar(255),
    "title"        varchar(255)   not null,
    "content"      varchar(10240) not null,
    "params"       varchar(255),
    "status"       int2           not null,
    "remark"       varchar(255),
    "version"      int4           not null default 0,
    "deleted"      int2           not null default 0,
    "gmt_create"   timestamp(6)   not null default current_timestamp,
    "gmt_modified" timestamp(6)            default current_timestamp,
    "tenant_id"    varchar(50)
)
;
alter table "us_notify_config_template"
    add constraint "us_notify_config_template_pk" primary key ("id");


drop table if exists "us_user_sync_config";
create table "us_user_sync_config"
(
    "id"           int8         not null,
    "sync_type"    int4,
    "sync_column"  varchar(50),
    "is_must"      int2,
    "is_ignore"    int2,
    "version"      int4         not null default 0,
    "deleted"      int2         not null default 0,
    "gmt_create"   timestamp(6) not null default current_timestamp,
    "gmt_modified" timestamp(6)          default current_timestamp,
    "tenant_id"    varchar(50)
);

drop table if exists "us_logged_user";
create table "us_logged_user"
(
    "aad_id"        varchar(50)  not null default '',
    "source_userid" varchar(128) not null default '',
    "create_time"   timestamp(6) not null default current_timestamp,
    "update_time"   timestamp(6)          default current_timestamp
);

----------------- 数据初始化 ------------------
insert into "us_department" ( "id", "source_dept_id", "source_dept_parent_id", "target_dept_id", "target_dept_parent_id", "name", "name_en", "order", "data_status", "sync_status", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id" )
    values ( 1771009993936130072, 'KPDP-TI-TISHKPCPMNM', 'KPFN-TI-SHKPCPMNM', '1083', '297', '科技与创新部', 'Technology and Innovation', 0, 1, 2, 0, 0, '2024-03-22 11:04:20.382236', '2024-03-22 11:13:51.744', 'ww4aaccf11cd9ae333' );
insert into "us_department" ( "id", "source_dept_id", "source_dept_parent_id", "target_dept_id", "target_dept_parent_id", "name", "name_en", "order", "data_status", "sync_status", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id" )
    values ( 1771009991272747041, 'KPPJ-HKPJ', 'KPBU-HKPJ', '1', '27', 'Hong Kong Project', null, 0, 2, 3, 0, 0, '2024-03-22 11:04:20.382', '2024-07-12 15:14:16.134', 'ww4aaccf11cd9ae333' );

insert into "us_user" ( "id", "source_userid", "name", "alias", "mobile", "position", "gender", "biz_mail", "telephone", "direct_leader", "avatar_mediaid", "enable", "extattr", "to_invite", "external_position", "external_profile", "address", "main_department", "data_status", "sync_status", "version", "deleted", "gmt_create", "gmt_modified", "target_userid", "tenant_id", "email" )
    values ( 1803365750457950209, '<EMAIL>', '万玉峰', 'Yu Feng, Fly Wan', null, 'Manager, IT', '1', null, '86-021-22311523', null, null, 1, '{"attrs":[{"name":"职务(中)","text":{"value":"经理-资讯科技"},"type":0}]}', 0, '员工', null, null, 1083, 1, 0, 0, 0, '2024-06-19 17:54:34.443', '2024-07-02 21:58:05.592', '<EMAIL>', 'ww4aaccf11cd9ae333', '<EMAIL>' );
insert into "us_user" ( "id", "source_userid", "name", "alias", "mobile", "position", "gender", "biz_mail", "telephone", "direct_leader", "avatar_mediaid", "enable", "extattr", "to_invite", "external_position", "external_profile", "address", "main_department", "data_status", "sync_status", "version", "deleted", "gmt_create", "gmt_modified", "target_userid", "tenant_id", "email" )
    values ( 1772920977787760641, '<EMAIL>', '聂明', 'Ming Nie', null, 'Storekeeper', '1', null, null, null, null, 1, '{"attrs":[{"name":"职务(中)","text":{"value":"仓库管理员"},"type":0}]}', 1, '员工', null, null, 1466, 1, 0, 0, 0, '2024-03-27 17:37:55.02', '2024-07-02 18:07:01.617', '<EMAIL>', 'ww4aaccf11cd9ae333', '<EMAIL>' );
insert into "us_user" ( "id", "source_userid", "name", "alias", "mobile", "position", "gender", "biz_mail", "telephone", "direct_leader", "avatar_mediaid", "enable", "extattr", "to_invite", "external_position", "external_profile", "address", "main_department", "data_status", "sync_status", "version", "deleted", "gmt_create", "gmt_modified", "target_userid", "tenant_id", "email" )
    values ( 1772920977804537858, '<EMAIL>', '吴嘉安', 'Jia An Wu', null, 'Receptionist', '2', null, null, null, null, 0, '{"attrs":[{"name":"职务(中)","text":{"value":"前台文员"},"type":0}]}', 1, '员工', null, null, 1466, 2, 0, 0, 0, '2024-03-27 17:37:55.02', '2024-03-28 10:34:04.026', '<EMAIL>', 'ww4aaccf11cd9ae333', '<EMAIL>' );

insert into "us_department_user" ( "id", "source_dept_id", "target_dept_id", "tartget_userid", "is_dept_leader", "dept_order", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id" )
    values ( 1803365751003209730, 'KPDP-TI-TISHKPCPMNM', '1083', '<EMAIL>', null, null, 0, 0, '2024-06-19 17:54:34.443', '2024-06-19 17:54:34.443', 'ww4aaccf11cd9ae333' );
insert into "us_department_user" ( "id", "source_dept_id", "target_dept_id", "tartget_userid", "is_dept_leader", "dept_order", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id" )
    values ( 1772920977901006849, 'KPDP-PM-PMSZKPDMSLQH', '1466', '<EMAIL>', null, null, 0, 0, '2024-03-27 17:37:55.02', '2024-03-27 17:37:55.02', 'ww4aaccf11cd9ae333' );
insert into "us_department_user" ( "id", "source_dept_id", "target_dept_id", "tartget_userid", "is_dept_leader", "dept_order", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id" )
    values ( 1772920977913589762, 'KPDP-PM-PMSZKPDMSLQH', '1466', '<EMAIL>', null, null, 0, 0, '2024-03-27 17:37:55.02', '2024-03-28 10:34:04.028', 'ww4aaccf11cd9ae333' );

insert into "us_job" ( "id", "job_id", "tenant_id", "cron", "remark", "version", "deleted", "gmt_create", "gmt_modified" )
    values ( 1, 'QW_USER_SYNC_JOB', 'ww4aaccf11cd9ae333', '0 0 0 * * ?', null, 0, 0, '2024-03-08 14:30:01.895', '2024-03-08 14:30:01.895' );

insert into "us_sync_log" ( "id", "url", "request", "result", "status", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id", "batch_no" )
    values ( 1771005133190508545, '/cgi-bin/department/create', '{"parentId":1,"name":"Hong Kong Centralised Functions","order":0}', '8', '0', 0, 0, '2024-03-22 10:45:02.093', '2024-03-22 10:45:02.093', 'ww4aaccf11cd9ae333', '4e153529-9f05-466f-be9b-ad4e5e3dfe82' );
insert into "us_sync_log" ( "id", "url", "request", "result", "status", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id", "batch_no" )
    values ( 1771005134545268737, '/cgi-bin/user/update', '{"parentId":1,"name":"Hong Kong Properties Management","order":0}', '9', '0', 0, 0, '2024-03-22 10:45:02.416', '2024-03-22 10:45:02.416', 'ww4aaccf11cd9ae333', '4e153529-9f05-466f-be9b-ad4e5e3dfe82' );

insert into "us_notify_config" ( "id", "notify_type", "is_open", "mail", "username", "password", "host", "port", "robot_key", "version", "deleted", "gmt_create", "gmt_modified", "to_user", "tenant_id", "ssl_enable", "time_out" )
    values ( 1807678483873587201, 2, 2, '<EMAIL>', '<EMAIL>', 'K1rry+Platform#2022!', 'smtp.mailrelay.cn', 465, null, 0, 0, '2024-07-01 15:31:50.254', '2024-07-01 15:31:50.254', '<EMAIL>,<EMAIL>', 'ww4aaccf11cd9ae333', 'f', null );
insert into "us_notify_config" ( "id", "notify_type", "is_open", "mail", "username", "password", "host", "port", "robot_key", "version", "deleted", "gmt_create", "gmt_modified", "to_user", "tenant_id", "ssl_enable", "time_out" )
    values ( 1, 2, 1, '<EMAIL>', '<EMAIL>', 'K1rry+Platform#2022!', 'smtp.mailrelay.cn', 465, null, 0, 0, '2024-02-19 11:43:11.754', '2024-02-19 11:43:11.754', '<EMAIL>,<EMAIL>', 'ww4aaccf11cd9ae333', 't', null );

insert into "us_notify_config_template" ( "id", "name", "code", "config_id", "nickname", "title", "content", "params", "status", "remark", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id" )
    values ( 1, '通讯录同步结果邮件模版', 'USER_SYNC_EMAIL_TEMPLATE', 1, null, '通讯录同步结果', '本次通讯录同步批次号{},同步成功部门数量{},同步失败部门数量{},同步成员成功数量{},同步成员失败数量{},详细信息查看同步记录', null, 1, null, 0, 0, '2024-02-19 11:45:21.49', '2024-02-19 11:45:21.49', 'ww4aaccf11cd9ae333' );

insert into "us_user_sync_config" ( "id", "sync_type", "sync_column", "is_must", "is_ignore", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id" )
    values ( 1, 1, 'userid', 1, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'ww4aaccf11cd9ae333' );
insert into "us_user_sync_config" ( "id", "sync_type", "sync_column", "is_must", "is_ignore", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id" )
    values ( 2, 1, 'name', 1, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'ww4aaccf11cd9ae333' );
insert into "us_user_sync_config" ( "id", "sync_type", "sync_column", "is_must", "is_ignore", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id" )
    values ( 3, 1, 'alias', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'ww4aaccf11cd9ae333' );
insert into "us_user_sync_config" ( "id", "sync_type", "sync_column", "is_must", "is_ignore", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id" )
    values ( 4, 1, 'mobile', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'ww4aaccf11cd9ae333' );
insert into "us_user_sync_config" ( "id", "sync_type", "sync_column", "is_must", "is_ignore", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id" )
    values ( 5, 1, 'department', 1, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'ww4aaccf11cd9ae333' );
insert into "us_user_sync_config" ( "id", "sync_type", "sync_column", "is_must", "is_ignore", "version", "deleted", "gmt_create", "gmt_modified", "tenant_id" )
    values ( 6, 1, 'order', 0, 0, 0, 0, '2024-02-18 10:26:08.032', '2024-02-18 10:26:08.032', 'ww4aaccf11cd9ae333' );
