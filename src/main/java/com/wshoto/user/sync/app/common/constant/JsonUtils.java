package com.wshoto.user.sync.app.common.constant;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * 用于JSON序列化和反序列化操作的实用类.
 *
 * <p>
 * 提供了使用预配置的Jackson ObjectMapper实例将Java对象转换为JSON字符串及反向转换的方法.
 *
 * <AUTHOR> Zhang
 * @since 1.0
 */
@Slf4j
@Getter
@RequiredArgsConstructor
public enum JsonUtils {

    /**
     * 具有自定义配置的主Jackson ObjectMapper实例.
     *
     * <p>
     * 包含对Java 8日期/时间类型的支持以及自定义序列化设置.
     */
    JACKSON(createObjectMapper());

    private final ObjectMapper objectMapper;

    /**
     * 将对象转换为其JSON字符串表示形式.
     *
     * @param o 要转换的对象
     * @return 对象的JSON字符串表示形式
     */
    public static String toJson(Object o) {
        return JACKSON.objToStr(o);
    }

    /**
     * 将JSON字符串转换为指定类的对象.
     *
     * @param str   要转换的JSON字符串
     * @param clazz 目标类类型
     * @param <T>   目标对象的类型
     * @return 包含反序列化对象的Optional，如果转换失败则为空
     */
    public static <T> Optional<T> fromJson(String str, Class<T> clazz) {
        return JACKSON.strToObj(str, clazz);
    }

    /**
     * 将JSON字符串转换为对象使用TypeReference.
     *
     * <p>
     * 对于反序列化泛型类型和集合很有用.
     *
     * @param str           要转换的JSON字符串
     * @param typeReference 描述目标类型的TypeReference
     * @param <T>           目标对象的类型
     * @return 包含反序列化对象的Optional，如果转换失败则为空
     */
    public static <T> Optional<T> fromJsonV2(String str, TypeReference<T> typeReference) {
        return JACKSON.strToObj(str, typeReference);
    }

    private static ObjectMapper createObjectMapper() {
        return new Jackson2ObjectMapperBuilder().serializationInclusion(JsonInclude.Include.NON_NULL)
                                                .failOnUnknownProperties(false)
                                                .failOnEmptyBeans(false)
                                                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                                                .serializerByType(LocalDateTime.class, new LocalDateTimeSerializer(
                                                        AppConstants.LOCAL_DATE_TIME_FORMATTER))
                                                .serializerByType(LocalDate.class, new LocalDateSerializer(
                                                        DateTimeFormatter.ISO_LOCAL_DATE))
                                                .deserializerByType(LocalDate.class, new LocalDateDeserializer(
                                                        DateTimeFormatter.ISO_LOCAL_DATE))
                                                .deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer(
                                                        AppConstants.LOCAL_DATE_TIME_FORMATTER))
                                                .serializerByType(LocalTime.class, new LocalTimeSerializer(
                                                        DateTimeFormatter.ISO_LOCAL_TIME))
                                                .deserializerByType(LocalTime.class, new LocalTimeDeserializer(
                                                        DateTimeFormatter.ISO_LOCAL_TIME))
                                                .build();
    }

    /**
     * 将对象转换为其JSON字符串表示形式.
     *
     * @param object 要转换的对象
     * @return 对象的JSON字符串表示形式
     * @throws JsonSerializationException 如果序列化过程中发生错误
     */
    private String objToStr(Object object) {
        try {
            return this.objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("将对象转换为JSON字符串时发生错误", e);
            return "";
        }
    }

    /**
     * 将JSON字符串转换为对象使用TypeReference.
     *
     * @param str           要转换的JSON字符串
     * @param typeReference 描述目标类型的TypeReference
     * @param <T>           目标对象的类型
     * @return 包含反序列化对象的Optional，如果转换失败则为空
     * @throws JsonSerializationException 如果反序列化过程中发生错误且不应被静默处理
     */
    private <T> Optional<T> strToObj(String str, TypeReference<T> typeReference) {
        if (str.isBlank()) {
            return Optional.empty();
        }
        try {
            return Optional.of(this.objectMapper.readValue(str, typeReference));
        } catch (JsonProcessingException e) {
            log.error("读取JSON字符串 {} 到类型 {} 时发生错误", str, typeReference, e);
            return Optional.empty();
        }
    }

    /**
     * 将JSON字符串转换为指定类的对象.
     *
     * @param str   要转换的JSON字符串
     * @param clazz 目标类类型
     * @param <T>   目标对象的类型
     * @return 包含反序列化对象的Optional，如果转换失败则为空
     * @throws JsonSerializationException 如果反序列化过程中发生错误且不应被静默处理
     */
    private <T> Optional<T> strToObj(String str, Class<T> clazz) {
        if (str.isBlank()) {
            return Optional.empty();
        }
        try {
            return Optional.of(this.objectMapper.readValue(str, clazz));
        } catch (JsonProcessingException e) {
            log.error("读取JSON字符串 {} 到类型 {} 时发生错误", str, clazz, e);
            return Optional.empty();
        }
    }

}
