package com.wshoto.user.sync.wx.cp.config;

import com.wshoto.user.sync.wx.cp.sdk.api.WxCpService;
import com.wshoto.user.sync.wx.cp.sdk.api.impl.WxCpServiceImpl;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxRuntimeException;
import com.wshoto.user.sync.wx.cp.sdk.config.impl.WxCpRedissonConfigImpl;
import jakarta.annotation.PostConstruct;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 多实例配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(WxCpProperties.class)
public class WxCpConfiguration {

    private final Map<String, WxCpService> cpServices = new ConcurrentHashMap<>();

    private final WxCpProperties properties;

    private final RedissonClient redissonClient;

    @Autowired
    public WxCpConfiguration(WxCpProperties properties, RedissonClient redissonClient) {
        this.properties = properties;
        this.redissonClient = redissonClient;
    }

    public WxCpService getCpService(String corpId) {
        WxCpService cpService = cpServices.get(corpId);
        return Optional.ofNullable(cpService)
                       .orElseThrow(() -> new WxRuntimeException("未配置此service"));
    }

    @PostConstruct
    public void initServices() {
        Map<String, WxCpServiceImpl> collect = properties.getAppConfigs()
                                                         .stream()
                                                         .map(a -> {
                                                             WxCpRedissonConfigImpl config =
                                                                     new WxCpRedissonConfigImpl(redissonClient,
                                                                                                "workRedis:");
                                                             config.setCorpId(a.getCorpId());
                                                             config.setCorpSecret(a.getSecret());
                                                             config.setAgentId(a.getAgentId());

                                                             var service = new WxCpServiceImpl();
                                                             service.setWxCpConfigStorage(config);
                                                             return service;
                                                         })
                                                         .collect(Collectors.toMap(
                                                                 service -> service.getWxCpConfigStorage()
                                                                                   .getCorpId(), a -> a));
        cpServices.putAll(collect);
    }

}
