package com.wshoto.user.sync.app.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wshoto.user.sync.app.dao.entity.NotifyConfigTemplate;
import com.wshoto.user.sync.app.dao.mapper.NotifyConfigTemplateMapper;
import com.wshoto.user.sync.app.service.NotifyConfigTemplateService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
@Service
public class NotifyConfigTemplateServiceImpl extends ServiceImpl<NotifyConfigTemplateMapper, NotifyConfigTemplate> implements NotifyConfigTemplateService {

}
