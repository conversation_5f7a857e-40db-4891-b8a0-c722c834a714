server:
  port: 8080

logging:
  level:
    org.springframework.web: INFO
    com.wshoto.user.sync: DEBUG
  config: classpath:log4j2.xml

#多实例配置
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *****************************************************************************************************************************************
    username: qa_wecom
    password: YSJ12OuzKkhLQ8nW2ijXRrJWKtAEg9PM
  redis:
    database: 0
    host: 127.0.0.1
    timeout: 5s
## mybatis配置项
mybatis-plus:
  mapper-locations: classpath:/mapper/**/*Mapper.xml
  global-config:
    banner: false
    db-config:
      id-type: assign_id
  configuration:
    cache-enabled: true
    default-executor-type: reuse
    log-impl: org.apache.ibatis.logging.log4j2.Log4j2Impl
  entry-ttl: 8

application:
  services:
    unifiedMessaging: https://dev-kip-service-internal.kerryonvip.com/unified-messaging-service

distributed:
  jobs:
    enabled: false

wechat:
  cp:
    appConfigs:
      - agentId: 1000001
        corpId: ww4aaccf11cd9ae333
        secret: SQEXIiiejb5nBMW1fKlgYKaVCeXxDf9IH1ZHUPFOIfQ
