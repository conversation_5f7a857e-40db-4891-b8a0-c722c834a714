package com.wshoto.user.sync.app;

import io.restassured.RestAssured;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.util.TestSocketUtils;
import org.springframework.util.function.SingletonSupplier;
import redis.embedded.RedisServer;

import java.io.IOException;
import java.io.UncheckedIOException;

@ActiveProfiles("test")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public abstract class BaseIntegrationTest {

    private static final SingletonSupplier<RedisServer> REDIS_SERVER_SUPPLIER =
            SingletonSupplier.of(BaseIntegrationTest::createRedis);

    static void start() {
        try {
            REDIS_SERVER_SUPPLIER.obtain()
                                 .start();
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    public static int getRedisPort() {
        return REDIS_SERVER_SUPPLIER.obtain()
                                    .ports()
                                    .get(0);
    }

    private static RedisServer createRedis() {
        try {
            return new RedisServer(TestSocketUtils.findAvailableTcpPort());
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    static {
        start();
    }

    @LocalServerPort
    private int port;

    @BeforeEach
    public void setupRestAssured() {
        RestAssured.port = port;
    }

    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry registry) {
        registry.add("spring.redis.port", BaseIntegrationTest::getRedisPort);
    }

}



