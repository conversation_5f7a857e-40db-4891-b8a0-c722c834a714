package com.wshoto.user.sync.app.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("us_notify_config_template")
public class NotifyConfigTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 模板名称
     */
    @TableField("name")
    private String name;

    /**
     * 模板编码
     */
    @TableField("code")
    private String code;

    /**
     * 异常通知配置id
     */
    @TableField("config_id")
    private Long configId;

    /**
     * 发送人名称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 模板标题
     */
    @TableField("title")
    private String title;

    /**
     * 模板内容
     */
    @TableField("content")
    private String content;

    /**
     * 参数数组
     */
    @TableField("params")
    private String params;

    /**
     * 开启状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 当前版本
     */
    @TableField("version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private Date gmtModified;


}
