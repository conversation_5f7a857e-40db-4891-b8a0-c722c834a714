package com.wshoto.user.sync.wx.cp.sdk.util.json;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpDepart;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpTag;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpUser;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxError;
import com.wshoto.user.sync.wx.cp.sdk.common.util.json.WxErrorAdapter;

/**
 * The type Wx cp gson builder.
 *
 * <AUTHOR>
 */
public class WxCpGsonHolder {

  private WxCpGsonHolder() {}

  private static final GsonBuilder INSTANCE = new GsonBuilder();
  private static final Gson GSON_INSTANCE;

  static {
    INSTANCE.disableHtmlEscaping();
    INSTANCE.registerTypeAdapter(WxCpDepart.class, new WxCpDepartGsonAdapter());
    INSTANCE.registerTypeAdapter(WxCpUser.class, new WxCpUserGsonAdapter());
    INSTANCE.registerTypeAdapter(WxError.class, new WxErrorAdapter());
    INSTANCE.registerTypeAdapter(WxCpTag.class, new WxCpTagGsonAdapter());
    GSON_INSTANCE = INSTANCE.create();
  }

  /**
   * Create gson.
   *
   * @return the gson
   */
  public static Gson get() {
    return GSON_INSTANCE;
  }

}
