package com.wshoto.user.sync.wx.cp.sdk.common.util.fs;

import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;

import static org.apache.commons.io.FileUtils.openOutputStream;

/**
 * <AUTHOR>
 */
public class FileUtils {

  private FileUtils() {}

  /**
   * 创建临时文件.
   *
   * @param inputStream 输入文件流
   * @param name        文件名
   * @param ext         扩展名
   * @param tmpDirFile  临时文件夹目录
   */
  public static File createTmpFile(InputStream inputStream, String name, String ext, File tmpDirFile)
    throws IOException {
    File resultFile = File.createTempFile(name, '.' + ext, tmpDirFile);

    resultFile.deleteOnExit();
    copyToFile(inputStream, resultFile);
    return resultFile;
  }

  private static void copyToFile(final InputStream source, final File destination) throws IOException {
    try (InputStream in = source; OutputStream out = openOutputStream(destination)) {
      IOUtils.copy(in, out);
    }
  }

  /**
   * 创建临时文件.
   *
   * @param inputStream 输入文件流
   * @param name        文件名
   * @param ext         扩展名
   */
  public static File createTmpFile(InputStream inputStream, String name, String ext) throws IOException {
    return createTmpFile(inputStream, name, ext, Files.createTempDirectory("wxjava-temp").toFile());
  }

}
