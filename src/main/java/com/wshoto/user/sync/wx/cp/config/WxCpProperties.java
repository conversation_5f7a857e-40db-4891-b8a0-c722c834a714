package com.wshoto.user.sync.wx.cp.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "wechat.cp")
public class WxCpProperties {

  private List<AppConfig> appConfigs;

  @Getter
  @Setter
  public static class AppConfig {

    /**
     * 设置企业微信的corpId
     */
    private String corpId;

    /**
     * 设置企业微信应用的Secret
     */
    private String secret;
    /**
     * 应用 agentId
     */
    private Integer agentId;
  }

}
