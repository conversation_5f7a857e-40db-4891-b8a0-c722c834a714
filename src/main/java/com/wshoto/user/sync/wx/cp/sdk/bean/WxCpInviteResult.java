package com.wshoto.user.sync.wx.cp.sdk.bean;

import com.google.gson.annotations.SerializedName;
import com.wshoto.user.sync.wx.cp.sdk.util.json.WxCpGsonHolder;
import lombok.Data;

import java.io.Serializable;

/**
 * 邀请成员的结果对象类.
 * 
 *
 * <AUTHOR>
 */
@Data
public class WxCpInviteResult implements Serializable {
  private static final long serialVersionUID = 1420065684270213578L;

  @Override
  public String toString() {
    return WxCpGsonHolder.get().toJson(this);
  }

  /**
   * From json wx cp invite result.
   *
   * @param json the json
   * @return the wx cp invite result
   */
  public static WxCpInviteResult fromJson(String json) {
    return WxCpGsonHolder.get().fromJson(json, WxCpInviteResult.class);
  }

  @SerializedName("errcode")
  private Integer errCode;

  @SerializedName("errmsg")
  private String errMsg;

  @SerializedName("invaliduser")
  private String[] invalidUsers;

  @SerializedName("invalidparty")
  private String[] invalidParties;

  @SerializedName("invalidtag")
  private String[] invalidTags;

}
