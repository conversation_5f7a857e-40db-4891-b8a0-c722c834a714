package com.wshoto.user.sync.app.common.config;

import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.zalando.logbook.CorrelationId;

import static com.wshoto.user.sync.app.common.config.WeComConversationFilter.MDC_CORRELATION_ID_KEY;

/**
 * 用于设置关联ID功能的配置类.
 *
 * <p>
 * 该类提供了一个用于生成关联ID的bean，用于在分布式系统中跨不同服务跟踪请求.
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Configuration(proxyBeanMethods = false)
public class CorrelationIdConfig {

    /**
     * 创建一个从MDC上下文中获取关联ID的bean.
     *
     * @return 提供当前关联ID的{@link CorrelationId}实例
     */
    @Bean
    public CorrelationId correlationId() {
        return r -> MDC.get(MDC_CORRELATION_ID_KEY);
    }

}
