package com.wshoto.user.sync.app.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.wshoto.user.sync.app.dao.entity.Department;
import com.wshoto.user.sync.app.dao.entity.DepartmentUser;
import com.wshoto.user.sync.app.dao.entity.User;
import com.wshoto.user.sync.app.service.DepartmentService;
import com.wshoto.user.sync.app.service.DepartmentUserService;
import com.wshoto.user.sync.app.service.SyncLogService;
import com.wshoto.user.sync.app.service.UserService;
import com.wshoto.user.sync.wx.cp.config.WxCpConfiguration;
import com.wshoto.user.sync.wx.cp.sdk.api.WxCpDepartmentService;
import com.wshoto.user.sync.wx.cp.sdk.api.WxCpService;
import com.wshoto.user.sync.wx.cp.sdk.api.WxCpUserService;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpUser;
import com.wshoto.user.sync.wx.cp.sdk.common.enums.ExceptionType;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxErrorException;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.ibatis.session.Configuration;
import org.instancio.Instancio;
import org.instancio.Select;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith({MockitoExtension.class})
class QwSyncComponentTest {

    private static final String TEST_EXTERNAL_JSON = """
            {
              "external_corp_name": "Acme Corp",
              "wechat_channels": {
                "nickname": "AcmeChannels"
              },
              "external_attr": [
                {
                  "type": 0,
                  "name": "Employee ID",
                  "text": {
                    "value": "EMP12345"
                  },
                  "web": {
                    "url": "https://example.com/employee",
                    "title": "Employee Profile"
                  },
                  "miniprogram": {
                    "appid": "wx9876543210abcd",
                    "pagepath": "/pages/profile/index",
                    "title": "Employee Mini Program"
                  }
                }
              ]
            }
            """;

    private static final String TEST_EXT_ATTR_JSON = """
            {
              "attrs": [
                {
                  "type": 0,
                  "name": "employee_name",
                  "text": {
                    "value": "John Doe"
                  },
                  "web": {
                    "url": "https://example.com/profile",
                    "title": "Profile Page"
                  },
                  "miniprogram": {
                    "appid": "wx1234567890abcd",
                    "pagepath": "/pages/home/<USER>",
                    "title": "Mini Program"
                  }
                }
              ]
            }
            """;

    @Mock
    UserService userService;

    @Mock
    DepartmentService departmentService;

    @Mock
    DepartmentUserService departmentUserService;

    @Mock
    SyncLogService syncLogService;

    @Mock
    WxCpDepartmentService wxCpDepartmentService;

    @Mock
    WxCpConfiguration wxCpConfiguration;

    @InjectMocks
    QwSyncComponent qwSyncComponent;

    @BeforeAll
    static void setUp() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new Configuration(), ""), DepartmentUser.class);
    }

    @Test
    @DisplayName("更新用户-成功处理")
    void updateUser_validInput_success() throws WxErrorException {
        // Arrange
        User user = Instancio.create(User.class);
        user.setExtattr(TEST_EXT_ATTR_JSON);
        user.setExternalProfile(TEST_EXTERNAL_JSON);
        user.setTargetUserid("12345");
        List<DepartmentUser> departmentUserList = Instancio.ofList(DepartmentUser.class)
                                                           .size(2)
                                                           .supply(Select.field(DepartmentUser::getTargetDeptId),
                                                                   () -> "1")
                                                           .create();

        WxCpUserService mockWxCpUserService = mock(WxCpUserService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mock(WxCpService.class));
        when(wxCpConfiguration.getCpService(any())
                              .getUserService()).thenReturn(mockWxCpUserService);

        // Act
        qwSyncComponent.updateUser(user, departmentUserList, "batchNo123", Map.of(), Map.of());

        // Assert
        verify(userService, times(1)).updateSyncStatusProgressing(user);
        verify(mockWxCpUserService, times(1)).update(any(WxCpUser.class));
        verify(userService, times(1)).updateSyncStatusSuccess(user);
        verify(syncLogService, times(1)).saveLog(any(), any(), any(), any(), eq(0), eq("batchNo123"));
    }

    @Test
    @DisplayName("更新用户-WxErrorException失败-记录错误")
    void updateUser_WxErrorException_logsError() throws WxErrorException {
        // Arrange
        User user = Instancio.create(User.class);
        user.setTargetUserid("12345");
        user.setExtattr("{}");
        user.setExternalProfile("{}");
        List<DepartmentUser> departmentUserList = Instancio.ofList(DepartmentUser.class)
                                                           .size(2)
                                                           .supply(Select.field(DepartmentUser::getTargetDeptId),
                                                                   () -> "1")
                                                           .create();

        WxCpUserService mockWxCpUserService = mock(WxCpUserService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mock(WxCpService.class));
        when(wxCpConfiguration.getCpService(any())
                              .getUserService()).thenReturn(mockWxCpUserService);
        doThrow(new WxErrorException("error")).when(mockWxCpUserService)
                                              .update(any(WxCpUser.class));

        // Act
        assertThatThrownBy(() -> qwSyncComponent.updateUser(user, departmentUserList, "batchNo123", Map.of(),
                                                            Map.of())).isInstanceOf(RuntimeException.class);

        // Assert
        verify(userService, times(1)).updateSyncStatusProgressing(user);
        verify(mockWxCpUserService, times(1)).update(any(WxCpUser.class));
        verify(userService, times(1)).updateSyncStatusFail(user);
        verify(syncLogService, times(1)).saveLog(eq(user.getTenantId()), any(), any(), any(), anyInt(),
                                                 eq("batchNo123"));
    }

    @Test
    @DisplayName("更新用户-通用异常-记录错误")
    void updateUser_generalError_logsError() {
        // Arrange
        User user = Instancio.create(User.class);
        user.setTargetUserid("12345");
        user.setExtattr(TEST_EXT_ATTR_JSON);
        user.setExternalProfile(TEST_EXTERNAL_JSON);
        List<DepartmentUser> departmentUserList = Instancio.ofList(DepartmentUser.class)
                                                           .size(2)
                                                           .supply(Select.field(DepartmentUser::getTargetDeptId),
                                                                   () -> "1")
                                                           .create();

        when(wxCpConfiguration.getCpService(any())).thenThrow(new RuntimeException("general error"));

        // Act
        qwSyncComponent.updateUser(user, departmentUserList, "batchNo123", Map.of(), Map.of());

        // Assert
        verify(userService, times(1)).updateSyncStatusProgressing(user);
        verify(userService, times(1)).updateSyncStatusFail(user);
        verify(syncLogService, times(1)).saveLog(eq(user.getTenantId()), any(), any(), any(), anyInt(),
                                                 eq("batchNo123"));
    }

    @Test
    @DisplayName("创建部门-父部门不是数字-抛出NumberFormatException")
    void createDepartment_invalidParentId_NumberFormatException() {
        // Arrange
        String invalidParentId = "invalidParentId";
        Department department = Instancio.create(Department.class);
        department.setTargetDeptId(null);
        department.setSourceDeptParentId(invalidParentId);

        // Act & Assert
        String batchNo = "batchNo123";
        assertThatThrownBy(() -> qwSyncComponent.createDepartment(department, batchNo)).isInstanceOf(
                                                                                               NumberFormatException.class)
                                                                                       .hasMessageContaining(
                                                                                               "For input string");

    }

    @Test
    @DisplayName("创建部门-父部门未找到-抛出UserSyncException")
    void createDepartment_parentDeptNotFound_throwsUserSyncException() throws WxErrorException {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setTargetDeptId(null);
        department.setTargetDeptParentId(null);
        department.setSourceDeptParentId("1");

        when(departmentService.queryBySourceDeptId(any())).thenReturn(null);
        WxCpService mockWxCpService = mock(WxCpService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mockWxCpService);
        WxCpDepartmentService mockDepartmentService = mock(WxCpDepartmentService.class);
        when(mockWxCpService.getDepartmentService()).thenReturn(mockDepartmentService);

        // Act & Assert
        qwSyncComponent.createDepartment(department, "batchNo123");

        verify(departmentService, times(1)).updateSyncStatusFail(any());
        verify(syncLogService, times(1)).saveLog(any(), any(), any(), any(),
                                                 eq(ExceptionType.PARENT_DEPT_NOT_FOUND.getCode()), any());
    }

    @Test
    @DisplayName("创建部门-父部门ID有效-成功处理")
    void createDepartment_validParentDeptId_success() throws WxErrorException {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setTargetDeptId(null);
        department.setTargetDeptParentId("1");

        Department parentDept = Instancio.create(Department.class);
        parentDept.setTargetDeptId("123");

        WxCpService mockWxCpService = mock(WxCpService.class);
        WxCpDepartmentService mockDepartmentService = mock(WxCpDepartmentService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mockWxCpService);
        when(mockWxCpService.getDepartmentService()).thenReturn(mockDepartmentService);
        when(mockDepartmentService.create(any())).thenReturn(1L);

        // Act
        qwSyncComponent.createDepartment(department, "batchNo123");

        // Assert
        verify(departmentService, times(1)).updateSyncStatusProgressing(any());
        verify(departmentService, times(1)).updateSyncStatusSuccess(any(), any());
        verify(departmentUserService, times(1)).updateTargetDeptId(any(), any(), any());
        verify(syncLogService, times(1)).saveLog(any(), any(), any(), any(), anyInt(), any());
        assertThat(department.getTargetDeptId()).isEqualTo("1");
    }

    @Test
    @DisplayName("更新部门-父部门ID有效-成功处理")
    void updateDepartment_validParentDeptId_success() throws WxErrorException {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setTargetDeptId("1");
        department.setTargetDeptParentId("123");
        Department parentDepartment = Instancio.create(Department.class);
        parentDepartment.setTargetDeptId("123");
        parentDepartment.setTargetDeptParentId("123456789");

        when(departmentService.getOne(any())).thenReturn(parentDepartment);

        WxCpDepartmentService mockWxCpDepartmentService = mock(WxCpDepartmentService.class);
        WxCpService mockWxCpService = mock(WxCpService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mockWxCpService);
        when(mockWxCpService.getDepartmentService()).thenReturn(mockWxCpDepartmentService);

        // Act
        qwSyncComponent.updateDepartment(department, "batchNo123");

        // Assert
        verify(departmentService, times(1)).updateSyncStatusProgressing(department);
        verify(mockWxCpDepartmentService, times(1)).update(any());
        verify(departmentService, times(1)).updateSyncStatusSuccess(any(), any());
        verify(syncLogService, times(1)).saveLog(any(), any(), any(), any(), anyInt(), eq("batchNo123"));
    }

    @Test
    @DisplayName("更新部门-WxErrorException失败-记录错误")
    void updateDepartment_updateFails_logsError() throws WxErrorException {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setTargetDeptId("1");
        department.setTargetDeptParentId("1");

        WxCpService mockWxCpService = mock(WxCpService.class);
        WxCpDepartmentService mockWxCpDepartmentService = mock(WxCpDepartmentService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mockWxCpService);
        when(mockWxCpService.getDepartmentService()).thenReturn(mockWxCpDepartmentService);
        doThrow(new WxErrorException("error")).when(mockWxCpDepartmentService)
                                              .update(any());

        // Act
        qwSyncComponent.updateDepartment(department, "batchNo123");

        // Assert
        verify(departmentService, times(1)).updateSyncStatusFail(department);
        verify(syncLogService, times(1)).saveLog(any(), any(), any(), any(), anyInt(), any());
    }

    @Test
    @DisplayName("删除部门-有效部门-成功处理")
    void deleteDepartment_validDepartment_success() throws WxErrorException {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setTargetDeptId("123");

        WxCpService mockWxCpService = mock(WxCpService.class);
        WxCpDepartmentService mockWxCpDepartmentService = mock(WxCpDepartmentService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mockWxCpService);
        when(mockWxCpService.getDepartmentService()).thenReturn(mockWxCpDepartmentService);

        // Act
        qwSyncComponent.deleteDepartment(department, "batchNo123");

        // Assert
        verify(departmentService, times(1)).updateSyncStatusProgressing(department);
        verify(mockWxCpDepartmentService, times(1)).delete(Long.valueOf(department.getTargetDeptId()));
        verify(departmentService, times(1)).deleteDepartmentSuccess(department);
        verify(departmentUserService, times(1)).deleteDepartmentUser(department);
        verify(syncLogService, times(1)).saveLog(any(), any(), eq(department.getTargetDeptId()), any(), eq(0),
                                                 eq("batchNo123"));
    }

    @Test
    @DisplayName("删除部门-WxErrorException失败-记录错误")
    void deleteDepartment_invalidDepartment_logsError() throws WxErrorException {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setTargetDeptId("123");

        WxCpService mockWxCpService = mock(WxCpService.class);
        WxCpDepartmentService mockWxCpDepartmentService = mock(WxCpDepartmentService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mockWxCpService);
        when(mockWxCpService.getDepartmentService()).thenReturn(mockWxCpDepartmentService);
        doThrow(new WxErrorException("error")).when(mockWxCpDepartmentService)
                                              .delete(Long.valueOf("123"));

        // Act
        qwSyncComponent.deleteDepartment(department, "batchNo123");

        // Assert
        verify(departmentService, times(1)).updateSyncStatusProgressing(department);
        verify(mockWxCpDepartmentService, times(1)).delete(Long.valueOf(department.getTargetDeptId()));
        verify(departmentService, times(1)).updateSyncStatusFail(department);
        verify(syncLogService, times(1)).saveLog(any(), any(), any(), any(), anyInt(), eq("batchNo123"));
    }

    @Test
    @DisplayName("删除部门-通用异常-记录失败")
    void deleteDepartment_generalError_logsFailure() {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setTargetDeptId("123");

        when(wxCpConfiguration.getCpService(any())).thenThrow(new RuntimeException("general error"));

        // Act
        qwSyncComponent.deleteDepartment(department, "batchNo123");

        // Assert
        verify(departmentService, times(1)).updateSyncStatusProgressing(any());
        verify(departmentService, times(1)).updateSyncStatusFail(any());
        verify(syncLogService, times(1)).saveLog(any(), any(), any(), any(),
                                                 eq(ExceptionType.DELETE_DEPT_ERROR.getCode()), eq("batchNo123"));
    }

    @Test
    @DisplayName("创建部门-WxErrorException失败-记录错误")
    void createDepartment_invalidWxErrorException_logsError() throws WxErrorException {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setTargetDeptId(null);
        department.setTargetDeptParentId("1");
        department.setSourceDeptParentId("1");

        WxCpService mockWxCpService = mock(WxCpService.class);
        WxCpDepartmentService mockWxCpDepartmentService = mock(WxCpDepartmentService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mockWxCpService);
        when(mockWxCpService.getDepartmentService()).thenReturn(mockWxCpDepartmentService);
        doThrow(new WxErrorException("error")).when(mockWxCpDepartmentService)
                                              .create(any());

        // Act
        qwSyncComponent.createDepartment(department, "batchNo123");

        // Assert
        verify(departmentService, times(1)).updateSyncStatusProgressing(department);
        verify(departmentService, times(1)).updateSyncStatusFail(department);
        verify(syncLogService, times(1)).saveLog(any(), any(), any(), any(), anyInt(), eq("batchNo123"));
    }

    @Test
    @DisplayName("创建部门-通用异常-记录失败")
    void createDepartment_genericException_logsError() {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setTargetDeptId(null);
        department.setTargetDeptParentId(null);

        when(wxCpConfiguration.getCpService(any())).thenThrow(new RuntimeException("general error"));

        // Act
        qwSyncComponent.createDepartment(department, "batchNo123");

        // Assert
        verify(departmentService, times(1)).updateSyncStatusFail(any());
        verify(syncLogService, times(1)).saveLog(any(), any(), any(), any(), anyInt(), eq("batchNo123"));
    }

    @Test
    @DisplayName("创建部门-父部门ID存在-成功更新")
    void createDepartment_withValidParentId_updatesDepartment() throws WxErrorException {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setSourceDeptParentId("1");
        department.setTargetDeptId(null);
        department.setTargetDeptParentId(null);

        Department parentDept = Instancio.create(Department.class);
        parentDept.setTargetDeptId("123");
        parentDept.setTargetDeptParentId(null);

        WxCpService mockWxCpService = mock(WxCpService.class);
        WxCpDepartmentService mockDepartmentService = mock(WxCpDepartmentService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mockWxCpService);
        when(mockWxCpService.getDepartmentService()).thenReturn(mockDepartmentService);
        when(departmentService.queryBySourceDeptId(any())).thenReturn(parentDept);
        when(mockDepartmentService.create(any())).thenReturn(456L);

        // Act
        qwSyncComponent.createDepartment(department, "batchNo123");

        // Assert
        assertThat(department.getTargetDeptId()).isEqualTo("456");
        verify(departmentService, times(1)).updateSyncStatusSuccess(department, "123");
        verify(syncLogService, times(1)).saveLog(any(), any(), any(), eq("456"), eq(0), eq("batchNo123"));
    }

    @Test
    @DisplayName("创建用户-成功处理")
    void createUser_validInput_success() throws WxErrorException {
        // Arrange
        User user = Instancio.create(User.class);
        user.setExtattr(TEST_EXT_ATTR_JSON);
        user.setExternalProfile(TEST_EXTERNAL_JSON);
        user.setTargetUserid("12345");
        List<DepartmentUser> departmentUserList = Instancio.ofList(DepartmentUser.class)
                                                           .size(2)
                                                           .supply(Select.field(DepartmentUser::getTargetDeptId),
                                                                   () -> "1")
                                                           .create();

        WxCpUserService mockWxCpUserService = mock(WxCpUserService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mock(WxCpService.class));
        when(wxCpConfiguration.getCpService(any())
                              .getUserService()).thenReturn(mockWxCpUserService);

        // Act
        qwSyncComponent.createUser(user, departmentUserList, "batchNo123", Map.of(), Map.of());

        // Assert
        verify(userService, times(1)).updateSyncStatusProgressing(user);
        verify(mockWxCpUserService, times(1)).create(any(WxCpUser.class));
        verify(userService, times(1)).updateSyncStatusSuccess(user);
        verify(syncLogService, times(1)).saveLog(any(), any(), any(), any(), eq(0), eq("batchNo123"));
    }

    @Test
    @DisplayName("创建用户-WxErrorException失败-记录错误")
    void createUser_WxErrorException_logsError() throws WxErrorException {
        // Arrange
        User user = Instancio.create(User.class);
        user.setTargetUserid("12345");
        user.setExtattr("{}");
        user.setExternalProfile("{}");
        List<DepartmentUser> departmentUserList = Instancio.ofList(DepartmentUser.class)
                                                           .size(2)
                                                           .supply(Select.field(DepartmentUser::getTargetDeptId),
                                                                   () -> "1")
                                                           .create();

        WxCpUserService mockWxCpUserService = mock(WxCpUserService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mock(WxCpService.class));
        when(wxCpConfiguration.getCpService(any())
                              .getUserService()).thenReturn(mockWxCpUserService);
        doThrow(new WxErrorException("error")).when(mockWxCpUserService)
                                              .create(any(WxCpUser.class));

        // Act
        assertThatThrownBy(() -> qwSyncComponent.createUser(user, departmentUserList, "batchNo123", Map.of(),
                                                            Map.of())).isInstanceOf(RuntimeException.class);

        // Assert
        verify(userService, times(1)).updateSyncStatusProgressing(user);
        verify(mockWxCpUserService, times(1)).create(any(WxCpUser.class));
        verify(userService, times(1)).updateSyncStatusFail(user);
        verify(syncLogService, times(1)).saveLog(eq(user.getTenantId()), any(), any(), any(), anyInt(),
                                                 eq("batchNo123"));
    }

    @Test
    @DisplayName("创建用户-通用异常-记录错误")
    void createUser_generalError_logsError() {
        // Arrange
        User user = Instancio.create(User.class);
        user.setTargetUserid("12345");
        user.setExtattr(TEST_EXT_ATTR_JSON);
        user.setExternalProfile(TEST_EXTERNAL_JSON);
        List<DepartmentUser> departmentUserList = Instancio.ofList(DepartmentUser.class)
                                                           .size(2)
                                                           .supply(Select.field(DepartmentUser::getTargetDeptId),
                                                                   () -> "1")
                                                           .create();

        when(wxCpConfiguration.getCpService(any())).thenThrow(new RuntimeException("general error"));

        // Act
        qwSyncComponent.createUser(user, departmentUserList, "batchNo123", Map.of(), Map.of());

        // Assert
        verify(userService, times(1)).updateSyncStatusProgressing(user);
        verify(userService, times(1)).updateSyncStatusFail(user);
        verify(syncLogService, times(1)).saveLog(eq(user.getTenantId()), any(), any(), any(), anyInt(),
                                                 eq("batchNo123"));
    }

    @Test
    @DisplayName("删除用户-有效用户-成功处理")
    void deleteUser_validInput_success() throws WxErrorException {
        // Arrange
        User user = Instancio.create(User.class);
        user.setTargetUserid("12345");
        List<DepartmentUser> departmentUserList = Instancio.ofList(DepartmentUser.class)
                                                           .size(2)
                                                           .supply(Select.field(DepartmentUser::getTargetDeptId),
                                                                   () -> "1")
                                                           .create();

        WxCpUserService mockWxCpUserService = mock(WxCpUserService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mock(WxCpService.class));
        when(wxCpConfiguration.getCpService(any())
                              .getUserService()).thenReturn(mockWxCpUserService);

        // Act
        qwSyncComponent.deleteUser(user, departmentUserList, "batchNo123");

        // Assert
        verify(userService, times(1)).updateSyncStatusProgressing(user);
        verify(mockWxCpUserService, times(1)).delete(any());
        verify(userService, times(1)).deleteUserSuccess(user);
        verify(syncLogService, times(1)).saveLog(any(), any(), any(), any(), anyInt(), eq("batchNo123"));
    }

    @Test
    @DisplayName("删除用户-WxErrorException失败-记录错误")
    void deleteUser_WxErrorException_logsError() throws WxErrorException {
        // Arrange
        User user = Instancio.create(User.class);
        user.setTargetUserid("12345");
        List<DepartmentUser> departmentUserList = Instancio.ofList(DepartmentUser.class)
                                                           .size(2)
                                                           .supply(Select.field(DepartmentUser::getTargetDeptId),
                                                                   () -> "1")
                                                           .create();

        WxCpUserService mockWxCpUserService = mock(WxCpUserService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mock(WxCpService.class));
        when(wxCpConfiguration.getCpService(any())
                              .getUserService()).thenReturn(mockWxCpUserService);

        doThrow(new WxErrorException("error")).when(mockWxCpUserService)
                                              .delete(any());

        // Act
        assertThatThrownBy(() -> qwSyncComponent.deleteUser(user, departmentUserList, "batchNo123")).isInstanceOf(
                RuntimeException.class);

        // Assert
        verify(userService, times(1)).updateSyncStatusProgressing(user);
        verify(mockWxCpUserService, times(1)).delete(any());
        verify(userService, times(1)).updateSyncStatusFail(user);
        verify(syncLogService, times(1)).saveLog(eq(user.getTenantId()), any(), eq(user.getTargetUserid()), any(),
                                                 anyInt(), eq("batchNo123"));
    }

    @Test
    @DisplayName("删除用户-通用异常-记录错误")
    void deleteUser_generalError_logsError() {
        // Arrange
        User user = Instancio.create(User.class);
        user.setTargetUserid("12345");
        List<DepartmentUser> departmentUserList = Instancio.ofList(DepartmentUser.class)
                                                           .size(2)
                                                           .supply(Select.field(DepartmentUser::getTargetDeptId),
                                                                   () -> "1")
                                                           .create();

        when(wxCpConfiguration.getCpService(any())).thenThrow(new RuntimeException("general error"));

        // Act
        qwSyncComponent.deleteUser(user, departmentUserList, "batchNo123");

        // Assert
        verify(userService, times(1)).updateSyncStatusProgressing(user);
        verify(userService, times(1)).updateSyncStatusFail(user);
        verify(syncLogService, times(1)).saveLog(eq(user.getTenantId()), any(), any(), any(), anyInt(),
                                                 eq("batchNo123"));
    }

    @Test
    @DisplayName("删除已登录用户(旧方式)-有效用户-成功删除")
    void deleteUserOldManner_validInput_success() throws WxErrorException {
        // Arrange
        User user = Instancio.create(User.class);
        user.setTargetUserid("12345");
        user.setTenantId("tenant123");
        user.setAadId("aad123");

        WxCpUserService mockWxCpUserService = mock(WxCpUserService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mock(WxCpService.class));
        when(wxCpConfiguration.getCpService(any())
                              .getUserService()).thenReturn(mockWxCpUserService);

        // Act
        qwSyncComponent.deleteUserOldManner(user, "batchNo123");

        // Assert
        verify(userService, times(1)).updateSyncStatusProgressing(user);
        verify(mockWxCpUserService, times(1)).delete(eq(user.getTargetUserid()));
        verify(userService, times(1)).deleteUserSuccess(user);
        verify(departmentUserService, times(1)).update(any(LambdaUpdateWrapper.class));
        verify(syncLogService, times(1)).saveLog(eq(user.getTenantId()), any(), eq(user.getTargetUserid()), any(),
                                                 eq(0), eq("batchNo123"));
    }

    @Test
    @DisplayName("删除已登录用户(旧方式)-WxErrorException失败-记录错误")
    void deleteUserOldManner_WxErrorException_logsError() throws WxErrorException {
        // Arrange
        User user = Instancio.create(User.class);
        user.setTargetUserid("12345");
        user.setTenantId("tenant123");
        user.setAadId("aad123");

        WxCpUserService mockWxCpUserService = mock(WxCpUserService.class);
        when(wxCpConfiguration.getCpService(any())).thenReturn(mock(WxCpService.class));
        when(wxCpConfiguration.getCpService(any())
                              .getUserService()).thenReturn(mockWxCpUserService);
        doThrow(new WxErrorException("error")).when(mockWxCpUserService)
                                              .delete(eq(user.getTargetUserid()));

        // Act
        assertThatThrownBy(() -> qwSyncComponent.deleteUserOldManner(user, "batchNo123")).isInstanceOf(
                RuntimeException.class);

        // Assert
        verify(userService, times(1)).updateSyncStatusProgressing(user);
        verify(mockWxCpUserService, times(1)).delete(eq(user.getTargetUserid()));
        verify(userService, times(1)).updateSyncStatusFail(user);
        verify(syncLogService, times(1)).saveLog(eq(user.getTenantId()), any(), eq(user.getTargetUserid()), any(),
                                                 anyInt(), eq("batchNo123"));
    }

    @Test
    @DisplayName("删除已登录用户(旧方式)-通用异常-记录失败")
    void deleteUserOldManner_generalError_logsFailure() {
        // Arrange
        User user = Instancio.create(User.class);
        user.setTargetUserid("12345");
        user.setTenantId("tenant123");
        user.setAadId("aad123");

        when(wxCpConfiguration.getCpService(any())).thenThrow(new RuntimeException("general error"));

        // Act
        qwSyncComponent.deleteUserOldManner(user, "batchNo123");

        // Assert
        verify(userService, times(1)).updateSyncStatusProgressing(user);
        verify(userService, times(1)).updateSyncStatusFail(user);
        verify(syncLogService, times(1)).saveLog(eq(user.getTenantId()), any(), any(), any(),
                                                 eq(ExceptionType.DELETE_USER_ERROR.getCode()), eq("batchNo123"));
    }

}