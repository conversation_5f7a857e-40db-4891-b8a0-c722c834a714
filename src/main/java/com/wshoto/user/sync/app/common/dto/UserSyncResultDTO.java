package com.wshoto.user.sync.app.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * User synchronization result data transfer object
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserSyncResultDTO {
    /**
     * Whether the synchronization was successful
     */
    private boolean successful;
    
    /**
     * Error message if synchronization failed
     */
    private String errorMessage;
}
