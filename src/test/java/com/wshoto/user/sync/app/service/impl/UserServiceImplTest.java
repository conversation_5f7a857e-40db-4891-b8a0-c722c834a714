package com.wshoto.user.sync.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.wshoto.user.sync.app.common.dto.UserSyncResultDTO;
import com.wshoto.user.sync.app.common.enums.DataStatusEnum;
import com.wshoto.user.sync.app.common.enums.SyncStatusEnum;
import com.wshoto.user.sync.app.component.QwSyncComponent;
import com.wshoto.user.sync.app.dao.entity.DepartmentUser;
import com.wshoto.user.sync.app.dao.entity.User;
import com.wshoto.user.sync.app.dao.entity.UserLoggedDTO;
import com.wshoto.user.sync.app.dao.entity.UserSyncConfig;
import com.wshoto.user.sync.app.dao.mapper.UserMapper;
import com.wshoto.user.sync.app.service.UserLoggedService;
import com.wshoto.user.sync.app.service.UserSyncConfigService;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.ibatis.session.Configuration;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * UserServiceImplTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Yu 2025-04-23 17:40:50
 **/
@SuppressWarnings({"unchecked", "unused"})
@ExtendWith(MockitoExtension.class)
class UserServiceImplTest {

    private static final String TENANT_ID = "test_tenant";

    private static final String BATCH_NO = "test_batch";

    @Mock
    QwSyncComponent qwSyncComponent;

    @Mock
    UserSyncConfigService userSyncConfigService;

    @Mock
    UserLoggedService userLoggedService;

    @Mock
    UserMapper userMapper;

    @InjectMocks
    UserServiceImpl userService;

    @BeforeAll
    static void setUp() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new Configuration(), ""), UserSyncConfig.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new Configuration(), ""), UserLoggedDTO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new Configuration(), ""), User.class);
    }

    @BeforeEach
    void setUpBaseMapper() {
        ReflectionTestUtils.setField(userService, "baseMapper", userMapper);
    }

    @Test
    @DisplayName("syncDeleteUser-无可删除用户-记录信息日志")
    void syncDeleteUser_noUsersToDelete_logsInfo() {
        // Arrange
        String tenantId = "tenant1";
        String batchNo = "batch123";

        // Act
        userService.syncDeleteUser(tenantId, batchNo);

        // Assert
        verify(qwSyncComponent, never()).deleteUser(any(), any(), any());
        verify(qwSyncComponent, never()).deleteUserOldManner(any(), any());
    }

    @ParameterizedTest
    @EnumSource(value = SyncStatusEnum.class, names = {"PROGRESSING", "SUCCESS", "FAIL"})
    @DisplayName("syncUserToWeCom-非初始状态用户应被跳过")
    void syncUserToWeCom_nonInitStatus_skipUser(SyncStatusEnum status) {
        // 准备测试数据
        User user = Instancio.create(User.class);
        user.setSyncStatus(status.getCode());
        user.setSourceUserid("test_user_" + status.name());

        List<User> userList = Collections.singletonList(user);
        List<DepartmentUser> departmentUserList = new ArrayList<>();

        // 执行测试方法
        Map<String, UserSyncResultDTO> result =
                userService.syncUserToWeCom(TENANT_ID, userList, departmentUserList, BATCH_NO);

        // 验证结果
        assertThat(result).isNotNull();

        // 验证没有调用企业微信API
        verify(qwSyncComponent, never()).createUser(any(), anyList(), anyString(), anyMap(), anyMap());
    }

    @ParameterizedTest
    @MethodSource("syncResultScenarios")
    @DisplayName("syncUserToWeCom-初始状态用户同步结果测试")
    void syncUserToWeCom_initStatus_syncResult(boolean syncResult, String errCode, String errMsg,
                                               SyncStatusEnum expectedStatus) {
        // 准备测试数据
        User user = Instancio.create(User.class);
        user.setSyncStatus(SyncStatusEnum.INIT.getCode());
        user.setSourceUserid("test_user");

        List<User> userList = Collections.singletonList(user);
        List<DepartmentUser> departmentUserList = new ArrayList<>();

        // 执行测试方法
        Map<String, UserSyncResultDTO> result =
                userService.syncUserToWeCom(TENANT_ID, userList, departmentUserList, BATCH_NO);

        // 验证结果
        assertThat(result).isNotNull();
    }

    @Test
    @DisplayName("syncUserToWeCom-初始状态用户-发生异常")
    void syncUserToWeCom_initStatus_exception() {
        // 准备测试数据
        User user = Instancio.create(User.class);
        user.setSyncStatus(SyncStatusEnum.INIT.getCode());

        List<User> userList = Collections.singletonList(user);
        List<DepartmentUser> departmentUserList = new ArrayList<>();

        // 执行测试方法
        Map<String, UserSyncResultDTO> result =
                userService.syncUserToWeCom(TENANT_ID, userList, departmentUserList, BATCH_NO);

        // 验证结果
        assertThat(result).isNotNull();
    }

    @Test
    @DisplayName("syncUserToWeCom-多用户混合状态-正确处理")
    void syncUserToWeCom_multipleUsersWithDifferentStatus() {
        // 准备测试数据 - 为每种状态创建一个用户
        List<User> userList = new ArrayList<>();

        for (DataStatusEnum status : DataStatusEnum.values()) {
            User user = Instancio.create(User.class);
            user.setSyncStatus(SyncStatusEnum.INIT.getCode());
            user.setDataStatus(status.getCode());
            userList.add(user);
        }

        List<DepartmentUser> departmentUserList = new ArrayList<>();

        // 执行测试方法
        var syncResult = userService.syncUserToWeCom(TENANT_ID, userList, departmentUserList, BATCH_NO);

        assertThat(syncResult).isNotEmpty();
    }

    @ParameterizedTest
    @MethodSource("batchSyncScenarios")
    @DisplayName("syncUserToWeCom-批量处理测试")
    void syncUserToWeCom_batchSync(List<User> users, List<UserSyncResultDTO> expectedResults, int expectedSuccessCount,
                                   int expectedFailCount) {
        // 准备测试数据
        List<DepartmentUser> departmentUserList = new ArrayList<>();

        // 执行测试方法
        Map<String, UserSyncResultDTO> result =
                userService.syncUserToWeCom(TENANT_ID, users, departmentUserList, BATCH_NO);

        assertThat(result).isNotNull();
    }

    @Test
    @DisplayName("syncDeleteUser-标记删除的用户-成功删除")
    void syncDeleteUser_usersMarkedForDeletion_successfulDeletion() {
        // Arrange
        String tenantId = "tenant1";
        String batchNo = "batch123";

        List<User> usersToDelete = Instancio.ofList(User.class)
                                            .size(2)
                                            .set(field(User::getDataStatus), DataStatusEnum.DELETE.getCode())
                                            .set(field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                                            .create();
        when(userService.list(any(LambdaQueryWrapper.class))).thenReturn(usersToDelete);

        List<UserLoggedDTO> loggedUsers = Instancio.ofList(UserLoggedDTO.class)
                                                   .size(1)
                                                   .set(field(UserLoggedDTO::getAadId), usersToDelete.get(0)
                                                                                                     .getAadId())
                                                   .create();
        when(userLoggedService.list()).thenReturn(loggedUsers);

        // Act
        userService.syncDeleteUser(tenantId, batchNo);

        // Assert
        verify(qwSyncComponent, times(1)).deleteUserOldManner(any(), any());
        verify(qwSyncComponent, times(1)).deleteUser(any(), any(), any());
    }

    @Test
    @DisplayName("syncDeleteUser-删除用户出错-记录错误日志")
    void syncDeleteUser_errorInDeletion_logsError() {
        // Arrange
        String tenantId = "tenant1";
        String batchNo = "batch123";

        List<User> usersToDelete = Instancio.ofList(User.class)
                                            .size(1)
                                            .set(field(User::getDataStatus), DataStatusEnum.DELETE.getCode())
                                            .set(field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                                            .create();
        when(userService.list(any(LambdaQueryWrapper.class))).thenReturn(usersToDelete);

        doThrow(new RuntimeException("Deletion error")).when(qwSyncComponent)
                                                       .deleteUser(any(), any(), any());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> userService.syncDeleteUser(tenantId, batchNo));

        verify(qwSyncComponent, times(1)).deleteUser(any(), any(), any());
    }

    @Test
    @DisplayName("syncUserToWeCom-正常入参-成功同步")
    void syncUserToWeCom_validInput_success() {
        // Arrange
        String tenantId = "tenant1";
        String batchNo = "batch123";
        List<User> userList = Instancio.ofList(User.class)
                                       .size(10)
                                       .create();
        List<DepartmentUser> departmentUserList = Instancio.ofList(DepartmentUser.class)
                                                           .size(10)
                                                           .create();
        when(userSyncConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(
                Instancio.ofList(UserSyncConfig.class)
                         .size(10)
                         .create());

        // Act
        Map<String, UserSyncResultDTO> results =
                userService.syncUserToWeCom(tenantId, userList, departmentUserList, batchNo);

        // Assert
        assertThat(results).hasSize(userList.size());
        results.values()
               .forEach(result -> assertThat(result.isSuccessful()).isTrue());
    }

    @Test
    @DisplayName("syncUserToWeCom-非初始状态用户-跳过用户")
    void syncUserToWeCom_invalidDataStatus_skipUser() {
        // Arrange
        String tenantId = "tenant1";
        String batchNo = "batch123";
        List<User> userList = Instancio.ofList(User.class)
                                       .size(1)
                                       .set(field(User::getSyncStatus), SyncStatusEnum.SUCCESS.getCode())
                                       .create();
        List<DepartmentUser> departmentUserList = Instancio.ofList(DepartmentUser.class)
                                                           .size(1)
                                                           .create();

        // Act
        Map<String, UserSyncResultDTO> results =
                userService.syncUserToWeCom(tenantId, userList, departmentUserList, batchNo);

        // Assert
        assertThat(results).hasSize(1);
        assertThat(results.get(userList.get(0)
                                       .getAadId())
                          .isSuccessful()).isTrue();
        assertThat(results.get(userList.get(0)
                                       .getAadId())
                          .getErrorMessage()).contains("Skipped");
    }

    @Test
    @DisplayName("syncUserToWeCom-用户创建异常-返回失败")
    void syncUserToWeCom_createUser_error_returnsFailure() {
        // Arrange
        String tenantId = "tenant1";
        String batchNo = "batch123";
        List<User> userList = Instancio.ofList(User.class)
                                       .size(1)
                                       .set(field(User::getSyncStatus), SyncStatusEnum.INIT.getCode())
                                       .set(field(User::getDataStatus), DataStatusEnum.CREATE.getCode())
                                       .create();
        List<DepartmentUser> departmentUserList = Instancio.ofList(DepartmentUser.class)
                                                           .size(1)
                                                           .create();
        when(userSyncConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        doThrow(new RuntimeException("Create user error")).when(qwSyncComponent)
                                                          .createUser(any(), any(), any(), any(), any());

        // Act
        Map<String, UserSyncResultDTO> results =
                userService.syncUserToWeCom(tenantId, userList, departmentUserList, batchNo);

        // Assert
        assertThat(results).hasSize(1);
        assertThat(results.get(userList.get(0)
                                       .getAadId())
                          .isSuccessful()).isFalse();
        assertThat(results.get(userList.get(0)
                                       .getAadId())
                          .getErrorMessage()).contains("Create user error");
    }

    @Test
    @DisplayName("deleteUserSuccess-有效输入-删除用户成功")
    void deleteUserSuccess_validInput_userDeleted() {
        // Arrange
        User user = Instancio.of(User.class)
                             .set(field(User::getId), 1L)
                             .set(field(User::getTenantId), "tenant1")
                             .set(field(User::getDeleted), 0)
                             .create();

        // Act
        userService.deleteUserSuccess(user);

        // Assert
        verify(userMapper, times(1)).update(any(LambdaUpdateWrapper.class));
    }


    private static Stream<Arguments> syncResultScenarios() {
        return Stream.of(
                // 成功场景：参数顺序(syncResult, errCode, errMsg, expectedStatus)
                Arguments.of(true, null, null, SyncStatusEnum.SUCCESS),
                // 失败场景
                Arguments.of(false, "40000", "创建用户失败", SyncStatusEnum.FAIL));
    }

    private static Stream<Arguments> batchSyncScenarios() {
        return Stream.of(
                // 全部成功场景
                Arguments.of(createTestUsers(3, 0, 0, 0), createSyncResults(3, 0), 3, 0),
                // 混合场景
                Arguments.of(createTestUsers(2, 1, 1, 1), createSyncResults(1, 1), 1, 1),
                // 全部失败场景
                Arguments.of(createTestUsers(3, 0, 0, 0), createSyncResults(0, 3), 0, 3));
    }

    /**
     * 创建测试用户数据
     */
    private static List<User> createTestUsers(int initCount, int progressingCount, int successCount, int failCount) {
        List<User> users = new ArrayList<>();

        // 创建INIT状态用户
        for (int i = 0; i < initCount; i++) {
            User user = new User();
            user.setSyncStatus(SyncStatusEnum.INIT.getCode());
            users.add(user);
        }

        // 创建PROGRESSING状态用户
        for (int i = 0; i < progressingCount; i++) {
            User user = new User();
            user.setSyncStatus(SyncStatusEnum.PROGRESSING.getCode());
            users.add(user);
        }

        // 创建SUCCESS状态用户
        for (int i = 0; i < successCount; i++) {
            User user = new User();
            user.setSyncStatus(SyncStatusEnum.SUCCESS.getCode());
            users.add(user);
        }

        // 创建FAIL状态用户
        for (int i = 0; i < failCount; i++) {
            User user = new User();
            user.setSyncStatus(SyncStatusEnum.FAIL.getCode());
            users.add(user);
        }

        return users;
    }

    /**
     * 创建同步结果
     */
    private static List<UserSyncResultDTO> createSyncResults(int successCount, int failCount) {
        List<UserSyncResultDTO> results = new ArrayList<>();

        // 创建成功结果
        for (int i = 0; i < successCount; i++) {
            results.add(UserSyncResultDTO.builder()
                                         .successful(true)
                                         .build());
        }

        // 创建失败结果
        for (int i = 0; i < failCount; i++) {
            results.add(UserSyncResultDTO.builder()
                                         .successful(false)
                                         .errorMessage("创建用户失败")
                                         .build());
        }

        return results;
    }

}