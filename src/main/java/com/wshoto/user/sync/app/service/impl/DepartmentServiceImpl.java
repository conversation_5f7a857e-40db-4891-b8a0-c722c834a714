package com.wshoto.user.sync.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wshoto.user.sync.app.common.config.WeComConversationFilter;
import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.common.enums.DataStatusEnum;
import com.wshoto.user.sync.app.common.enums.SyncStatusEnum;
import com.wshoto.user.sync.app.component.QwSyncComponent;
import com.wshoto.user.sync.app.dao.entity.Department;
import com.wshoto.user.sync.app.dao.mapper.DepartmentMapper;
import com.wshoto.user.sync.app.service.DepartmentService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
@Slf4j
@Service
public class DepartmentServiceImpl extends ServiceImpl<DepartmentMapper, Department> implements DepartmentService {

    @Resource
    private DepartmentMapper departmentMapper;

    @Resource
    private QwSyncComponent qwSyncComponent;

    @Override
    public void syncToWeCom(List<Department> departmentList, String batchNo) {
        try {
            WeComConversationFilter.refreshCorrelationId();
            log.info("批次: {}, 待同步部门总数: {}", batchNo, departmentList.size());

            // 统计各状态部门数量
            int deleteCount = 0;
            int createCount = 0;
            int updateCount = 0;
            int skipCount = 0;

            // 先处理删除数据，从尾部处理
            for (int i = departmentList.size() - 1; i >= 0; i--) {
                Department department = departmentList.get(i);
                if (DataStatusEnum.DELETE.getCode()
                                         .equals(department.getDataStatus()) && SyncStatusEnum.INIT.getCode()
                                                                                                   .equals(department.getSyncStatus())) {
                    qwSyncComponent.deleteDepartment(department, batchNo);
                    deleteCount++;
                }
            }

            // 再处理其他数据，从头部处理
            for (Department department : departmentList) {
                if (JobConstant.ROOT_DEPT.equals(department.getTargetDeptId())) {
                    log.info("批次: {}, 部门 {} 是根部门, 跳过", batchNo, department.getName());
                    skipCount++;
                    continue;
                }
                if (!SyncStatusEnum.INIT.getCode()
                                        .equals(department.getSyncStatus())) {
                    log.debug("批次: {}, 部门 {} 已同步, 跳过", batchNo, department.getName());
                    skipCount++;
                    continue;
                }
                if (DataStatusEnum.CREATE.getCode()
                                         .equals(department.getDataStatus())) {
                    log.info("批次: {}, 开始创建部门, 部门名称: {}, 源部门ID: {}", batchNo, department.getName(),
                             department.getSourceDeptId());
                    qwSyncComponent.createDepartment(department, batchNo);
                    createCount++;
                } else if (DataStatusEnum.UPDATE.getCode()
                                                .equals(department.getDataStatus())) {
                    log.info("批次: {}, 开始更新部门, 部门名称: {}, 源部门ID: {}", batchNo, department.getName(),
                             department.getSourceDeptId());
                    qwSyncComponent.updateDepartment(department, batchNo);
                    updateCount++;
                }
            }

            log.info("批次: {}, 部门同步完成 - 总数: {}, 删除: {}, 创建: {}, 更新: {}, 跳过: {}", batchNo,
                     departmentList.size(), deleteCount, createCount, updateCount, skipCount);
        } catch (Exception e) {
            log.error("批次: {}, 部门同步异常: {}", batchNo, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Department queryBySourceDeptId(String sourceDeptId) {
        List<Department> departmentList = departmentMapper.selectList(
                new LambdaQueryWrapper<Department>().eq(Department::getSourceDeptId, sourceDeptId)
                                                    .eq(Department::getDeleted, JobConstant.DELETED_NO));
        return CollUtil.isNotEmpty(departmentList) ? departmentList.get(0) : null;
    }

    @Override
    public void updateSyncStatusProgressing(Department department) {
        departmentMapper.update(new LambdaUpdateWrapper<Department>().eq(Department::getId, department.getId())
                                                                     .eq(Department::getTenantId,
                                                                         department.getTenantId())
                                                                     .eq(Department::getDeleted, JobConstant.DELETED_NO)
                                                                     .eq(Department::getSyncStatus,
                                                                         SyncStatusEnum.INIT.getCode())
                                                                     .set(Department::getSyncStatus,
                                                                          SyncStatusEnum.PROGRESSING.getCode())
                                                                     .set(Department::getGmtModified, new Date()));
    }

    @Override
    public void updateSyncStatusSuccess(Department department, String parentDeptId) {
        departmentMapper.update(new LambdaUpdateWrapper<Department>().eq(Department::getId, department.getId())
                                                                     .eq(Department::getTenantId,
                                                                         department.getTenantId())
                                                                     .eq(Department::getDeleted, JobConstant.DELETED_NO)
                                                                     .set(Department::getTargetDeptId,
                                                                          department.getTargetDeptId())
                                                                     .set(Department::getTargetDeptParentId,
                                                                          parentDeptId)
                                                                     .set(Department::getSyncStatus,
                                                                          SyncStatusEnum.SUCCESS.getCode())
                                                                     .set(Department::getGmtModified, new Date()));
    }

    @Override
    public void updateSyncStatusFail(Department department) {
        departmentMapper.update(
                new LambdaUpdateWrapper<Department>().eq(Department::getTenantId, department.getTenantId())
                                                     .eq(Department::getDeleted, JobConstant.DELETED_NO)
                                                     .eq(Department::getId, department.getId())
                                                     .set(Department::getSyncStatus, SyncStatusEnum.FAIL.getCode())
                                                     .set(Department::getGmtModified, new Date()));
    }

    @Override
    public void deleteDepartmentSuccess(Department department) {
        departmentMapper.update(
                new LambdaUpdateWrapper<Department>().eq(Department::getTenantId, department.getTenantId())
                                                     .eq(Department::getDeleted, JobConstant.DELETED_NO)
                                                     .eq(Department::getId, department.getId())
                                                     .set(Department::getDeleted, JobConstant.DELETED_YES)
                                                     .set(Department::getSyncStatus, SyncStatusEnum.SUCCESS.getCode())
                                                     .set(Department::getGmtModified, new Date()));
        log.info("deleted_us_department: {}:{}", department.getSourceDeptId(), department.getTargetDeptId());
    }

}
