package com.wshoto.user.sync.app.common.constant;

import com.google.common.collect.Lists;

import java.util.List;

import static com.wshoto.user.sync.wx.cp.sdk.constant.WxCpApiPathConsts.Department.*;
import static com.wshoto.user.sync.wx.cp.sdk.constant.WxCpApiPathConsts.User.*;

public class JobConstant {

    public static final String QW_USER_SYNC_JOB_ID = "QW_USER_SYNC_JOB";

    public static final int DELETED_YES = 1;
    public static final int DELETED_NO = 0;


    public static final List<String> DEPT_URL = Lists.newArrayList(DEPARTMENT_CREATE,DEPARTMENT_UPDATE,DEPARTMENT_DELETE);

    public static final List<String> USER_URL = Lists.newArrayList(USER_CREATE,USER_UPDATE,USER_DELETE);

    public static final String ROOT_DEPT = "1";
}
