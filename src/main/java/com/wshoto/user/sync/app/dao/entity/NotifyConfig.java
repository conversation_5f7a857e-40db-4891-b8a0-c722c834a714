package com.wshoto.user.sync.app.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 同步通知配置
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("us_notify_config")
public class NotifyConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 通知类型  1群消息 2邮件
     */
    @TableField("notify_type")
    private Integer notifyType;

    /**
     * 是否开启 1开启 0关闭
     */
    @TableField("is_open")
    private Integer isOpen;

    /**
     * 邮箱
     */
    @TableField("mail")
    private String mail;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * SMTP 服务器域名
     */
    @TableField("host")
    private String host;

    /**
     * SMTP 服务器端口
     */
    @TableField("port")
    private Long port;

    /**
     * 是否开启 SSL
     */
    @TableField("ssl_enable")
    private Boolean sslEnable;

    /**
     * 超时时间 毫秒
     */
    @TableField("time_out")
    private Integer timeOut;

    /**
     * 机器人密钥key
     */
    @TableField("robot_key")
    private String robotKey;

    /**
     * 当前版本
     */
    @TableField("version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private Date gmtModified;

    /**
     * 发送人或@人以,分割
     */
    @TableField("to_user")
    private String toUser;


}
