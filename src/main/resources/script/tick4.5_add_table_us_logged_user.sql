-- ----------------------------
-- Table structure for us_logged_user
-- ----------------------------
DROP TABLE IF EXISTS "us_logged_user";
CREATE TABLE "us_logged_user" (
    "aad_id" varchar(50) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
    "source_userid" varchar(128) COLLATE "pg_catalog"."default" NOT NULL  DEFAULT ''::character varying,
    "create_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "us_logged_user"."aad_id" IS 'microsoft AAD系统提供的uuid';
COMMENT ON COLUMN "us_logged_user"."source_userid" IS '员工id，对应microsoft AAD系统提供的UPN，AAD系统限制最大长度为113字符';
COMMENT ON COLUMN "us_logged_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "us_logged_user"."update_time" IS '修改时间';
COMMENT ON TABLE "us_logged_user" IS '已登录成员表';

-- ----------------------------
-- Primary Key structure for table us_logged_user
-- ----------------------------
ALTER TABLE "us_logged_user" ADD CONSTRAINT "pk_us_logged_user" PRIMARY KEY ("aad_id");