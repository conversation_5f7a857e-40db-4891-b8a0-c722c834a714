package com.wshoto.user.sync.wx.cp.sdk.common.redis;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@RequiredArgsConstructor
@Slf4j
public class RedissonWxRedisOps implements WxRedisOps {

  private final RedissonClient redissonClient;
  // 本地锁，用于Redis锁获取失败时的降级处理
  private final ReentrantLock localLock = new ReentrantLock();

  @Override
  public String getValue(String key) {
    try {
      Object value = redissonClient.getBucket(key).get();
      return value == null ? null : value.toString();
    } catch (Exception e) {
      log.error("Redis获取值异常，key: {}, 异常信息: {}", key, e.getMessage(), e);
      return null;
    }
  }

  @Override
  public void setValue(String key, String value, int expire, TimeUnit timeUnit) {
    try {
      RBucket<String> bucket = redissonClient.getBucket(key);
      // 先设置值
      bucket.set(value);
      
      // 如果需要设置过期时间
      if (expire > 0) {
        // 使用Duration API代替废弃的方法
        Duration duration = Duration.ofMillis(timeUnit.toMillis(expire));
        boolean result = bucket.expire(duration);
        if (!result) {
          logExpireFailure(key, expire);
        }
      }
    } catch (Exception e) {
      log.error("Redis设置值异常，key: {}, value: {}, expire: {}, 异常信息: {}", 
                key, value, expire, e.getMessage(), e);
    }
  }

  @Override
  public Long getExpire(String key) {
    try {
      long expire = redissonClient.getBucket(key).remainTimeToLive();
      if (expire > 0) {
        expire = expire / 1000;
      }
      return expire;
    } catch (Exception e) {
      log.error("Redis获取过期时间异常，key: {}, 异常信息: {}", key, e.getMessage(), e);
      return 0L;
    }
  }

  @Override
  public void expire(String key, int expire, TimeUnit timeUnit) {
    try {
      // 使用Duration API代替废弃的方法
      Duration duration = Duration.ofMillis(timeUnit.toMillis(expire));
      boolean result = redissonClient.getBucket(key).expire(duration);
      if (!result) {
        logExpireFailure(key, expire);
      }
    } catch (Exception e) {
      log.error("Redis设置过期时间异常，key: {}, expire: {}, 异常信息: {}", 
                key, expire, e.getMessage(), e);
    }
  }

  @Override
  public Lock getLock(String key) {
    try {
      return redissonClient.getLock(key);
    } catch (Exception e) {
      log.error("Redis获取锁异常，key: {}, 异常信息: {}", key, e.getMessage(), e);
      // 返回本地锁作为降级策略
      return localLock;
    }
  }
  
  /**
   * 记录设置过期时间失败的日志
   *
   * @param key 键
   * @param expire 过期时间
   */
  private void logExpireFailure(String key, int expire) {
    log.error("Redis设置过期时间失败，key: {}, expire: {}", key, expire);
  }
}
