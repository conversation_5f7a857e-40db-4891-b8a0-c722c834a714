package com.wshoto.user.sync.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wshoto.user.sync.app.dao.entity.SyncLog;

/**
 * <p>
 * 同步日志 服务类
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
public interface SyncLogService extends IService<SyncLog> {

    /**
     * 保存企微调用流水
     *
     * @param tenantId
     * @param url
     * @param request
     * @param response
     * @param code
     * @param batchNo
     */
    void saveLog(String tenantId, String url, String request, String response, int code, String batchNo);
}
