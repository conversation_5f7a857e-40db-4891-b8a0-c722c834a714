package com.wshoto.user.sync.wx.cp.sdk.common.util.json;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.wshoto.user.sync.wx.cp.sdk.common.bean.WxAccessToken;
import com.wshoto.user.sync.wx.cp.sdk.common.bean.WxNetCheckResult;
import com.wshoto.user.sync.wx.cp.sdk.common.bean.result.WxMediaUploadResult;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxError;

import java.util.Objects;

/**
 * .
 * <AUTHOR>
 */
public class WxGsonBuilder {
  private static final GsonBuilder INSTANCE = new GsonBuilder();
  private static Gson gsonInstance;

 private WxGsonBuilder() {}

  static {
    INSTANCE.disableHtmlEscaping();
    INSTANCE.registerTypeAdapter(WxAccessToken.class, new WxAccessTokenAdapter());
    INSTANCE.registerTypeAdapter(WxError.class, new WxErrorAdapter());
    INSTANCE.registerTypeAdapter(WxMediaUploadResult.class, new WxMediaUploadResultAdapter());
    INSTANCE.registerTypeAdapter(WxNetCheckResult.class, new WxNetCheckResultGsonAdapter());

  }

  public static Gson create() {
    if (Objects.isNull(gsonInstance)) {
      synchronized (INSTANCE) {
        if (Objects.isNull(gsonInstance)) {
          gsonInstance = INSTANCE.create();
        }
      }
    }
    return gsonInstance;
  }

}
