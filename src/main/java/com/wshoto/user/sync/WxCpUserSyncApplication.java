package com.wshoto.user.sync;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 在配置目录下，分为single和mutil目录，代表单实例和多实例方式，请自行选择配置方式
 *
 * <AUTHOR>
 */
@EnableScheduling
@EnableTransactionManagement
@EnableFeignClients
@SpringBootApplication(scanBasePackages = {"com.wshoto"})
@MapperScan({"com.wshoto.user.sync.app.dao.mapper"})
public class WxCpUserSyncApplication {

  public static void main(String[] args) {
    SpringApplication.run(WxCpUserSyncApplication.class, args);
  }

}
