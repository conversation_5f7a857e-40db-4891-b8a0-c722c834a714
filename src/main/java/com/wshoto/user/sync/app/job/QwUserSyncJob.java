package com.wshoto.user.sync.app.job;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.wshoto.user.sync.app.common.config.WeComConversationFilter;
import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.common.dto.UserSyncResultDTO;
import com.wshoto.user.sync.app.common.enums.DataStatusEnum;
import com.wshoto.user.sync.app.common.enums.SyncStatusEnum;
import com.wshoto.user.sync.app.common.util.DepartmentUtils;
import com.wshoto.user.sync.app.dao.entity.Department;
import com.wshoto.user.sync.app.dao.entity.DepartmentUser;
import com.wshoto.user.sync.app.dao.entity.NotifyConfig;
import com.wshoto.user.sync.app.dao.entity.NotifyConfigTemplate;
import com.wshoto.user.sync.app.dao.entity.SyncLog;
import com.wshoto.user.sync.app.dao.entity.User;
import com.wshoto.user.sync.app.dao.entity.UserLoggedDTO;
import com.wshoto.user.sync.app.feign.clients.MessageClient;
import com.wshoto.user.sync.app.feign.entity.EmailReplyVo;
import com.wshoto.user.sync.app.feign.entity.EmailSendCommandVo;
import com.wshoto.user.sync.app.feign.entity.RespWrapVo;
import com.wshoto.user.sync.app.service.DepartmentService;
import com.wshoto.user.sync.app.service.DepartmentUserService;
import com.wshoto.user.sync.app.service.NotifyConfigService;
import com.wshoto.user.sync.app.service.NotifyConfigTemplateService;
import com.wshoto.user.sync.app.service.SyncLogService;
import com.wshoto.user.sync.app.service.UserLoggedService;
import com.wshoto.user.sync.app.service.UserService;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxCpErrorMsgEnum;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 企微通讯录同步
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class QwUserSyncJob implements SyncJobHandler {

    @Resource
    private DepartmentService departmentService;

    @Resource
    private DepartmentUserService departmentUserService;

    @Resource
    private UserService userService;

    @Resource
    private UserLoggedService userLoggedService;

    @Resource
    private SyncLogService syncLogService;

    @Resource
    private NotifyConfigService notifyConfigService;

    @Resource
    private NotifyConfigTemplateService notifyConfigTemplateService;

    @Resource
    private MessageClient messageClient;

    /**
     * 同步企微任务
     *
     * @param tenantId 租户ID
     */
    @Override
    public String executeTask(String tenantId) {
        return executeTask(tenantId, null);
    }

    /**
     * 同步企微任务
     *
     * @param tenantId 租户ID
     * @param specificAadIds 指定需要同步的AAD ID列表，为空时同步所有用户
     */
    public String executeTask(String tenantId, List<String> specificAadIds) {
        String batchNo = null;
        try {
            WeComConversationFilter.setLoggingContext();
            batchNo = UUID.randomUUID()
                    .toString();
            
            if (CollectionUtils.isEmpty(specificAadIds)) {
                log.info("批次: {}, 开始执行企微用户同步任务", batchNo);
            } else {
                log.info("批次: {}, 开始执行企微用户同步任务，指定同步 {} 个用户", batchNo, specificAadIds.size());
            }

            // 初始化根部门数据
            Department rootDepartment = departmentService.getOne(
                    new LambdaQueryWrapper<Department>().eq(Department::getTenantId, tenantId)
                            .eq(Department::getDeleted, JobConstant.DELETED_NO)
                            .eq(Department::getTargetDeptId, JobConstant.ROOT_DEPT)
                            .last("limit 1"));
            if (rootDepartment == null) {
                rootDepartment = new Department();
                rootDepartment.setTenantId(tenantId);
                rootDepartment.setName(JobConstant.ROOT_DEPT);
                rootDepartment.setTargetDeptId(JobConstant.ROOT_DEPT);
                rootDepartment.setTargetDeptParentId("0");
                rootDepartment.setSyncStatus(SyncStatusEnum.SUCCESS.getCode());
                rootDepartment.setDataStatus(0);
                departmentService.save(rootDepartment);
                log.info("批次: {}, 根部门不存在, 已创建新根部门, ID: {}", batchNo, rootDepartment.getId());
            }

            // 同步删除用户
            userService.syncDeleteUser(tenantId, batchNo);

            // 获取并同步部门
            List<Department> departmentList = departmentService.list(
                    new LambdaQueryWrapper<Department>().eq(Department::getTenantId, tenantId)
                            .eq(Department::getDeleted, JobConstant.DELETED_NO)
                            .orderByAsc(Department::getId));
            log.info("批次: {}, 待同步部门数量: {}", batchNo, departmentList.size());

            // 部门根据上下级排序处理
            DepartmentUtils departmentUtils = new DepartmentUtils();
            List<Department> departmentTree = departmentUtils.generateTree(departmentList);
            departmentService.syncToWeCom(departmentTree, batchNo);

            syncStuff(tenantId, batchNo, specificAadIds);

            log.info("批次: {}, 企微用户同步任务执行完成", batchNo);
            return batchNo;
        } catch (Exception e) {
            log.error("批次: {}, 企微用户同步任务执行异常: {}", batchNo, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 同步企微用户
     *
     * @param tenantId 租户ID
     * @param batchNo 批次号
     */
    public void syncStuff(String tenantId, String batchNo) {
        syncStuff(tenantId, batchNo, null);
    }

    /**
     * 同步企微用户
     *
     * @param tenantId 租户ID
     * @param batchNo 批次号
     * @param specificAadIds 指定需要同步的AAD ID列表，为空时同步所有用户
     */
    public void syncStuff(String tenantId, String batchNo, List<String> specificAadIds) {
        WeComConversationFilter.refreshCorrelationId();
        log.info("批次: {}, 开始同步用户", batchNo);
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<User>()
                .eq(User::getTenantId, tenantId)
                .eq(User::getDeleted, JobConstant.DELETED_NO);
        
        // 如果指定了AAD ID列表，则添加AAD ID过滤条件
        if (!CollectionUtils.isEmpty(specificAadIds)) {
            queryWrapper.in(User::getAadId, specificAadIds);
        } else {
            // 只有在未指定AAD ID时，才添加同步状态和数据状态过滤条件
            queryWrapper.eq(User::getSyncStatus, SyncStatusEnum.INIT.getCode())
                    .in(User::getDataStatus,
                            List.of(DataStatusEnum.CREATE.getCode(),
                                    DataStatusEnum.UPDATE.getCode()));
        }
        
        List<User> userList = userService.list(queryWrapper);
        log.info("批次: {}, 待同步用户数量: {}", batchNo, userList.size());

        if (!CollectionUtils.isEmpty(userList)) {
            // 过滤已登录用户
            List<UserLoggedDTO> userLoggedDTOList = userLoggedService.list();
            Set<String> userLoggedAadIds = CollectionUtils.isEmpty(userLoggedDTOList) ? Collections.emptySet()
                    : userLoggedDTOList.stream()
                            .map(UserLoggedDTO::getAadId)
                            .collect(Collectors.toSet());

            int originalSize = userList.size();
            userList = userList.stream()
                               .filter(stuff -> !userLoggedAadIds.contains(stuff.getAadId())
                                       || stuff.getEnable() == 0)
                               .toList();

            log.info("批次: {}, 过滤已登录用户后, 同步用户数量: {} -> {}", batchNo, originalSize, userList.size());

            Map<String, String> loggedUserEmailMap = userLoggedDTOList.stream()
                    .collect(Collectors.toMap(
                            UserLoggedDTO::getAadId,
                            UserLoggedDTO::getSourceUserid,
                            (email1, email2) -> email1));

            if (!userList.isEmpty()) {
                updateDirectLeadersForLoggedUsers(userList, userLoggedAadIds, loggedUserEmailMap);

                // 分批处理用户
                List<List<User>> partition = Lists.partition(userList, 1000);
                log.info("批次: {}, 用户分批数量: {}, 每批最大用户数: 1000", batchNo, partition.size());

                processUserBatches(tenantId, batchNo, partition);
            }
        }
    }

    /**
     * Synchronizes specific users identified by their AAD IDs
     *
     * @param aadIds List of AAD IDs to synchronize
     * @return Detailed synchronization result report
     */
    public SyncReport syncUsersByAadIds(String tenantId, List<String> aadIds) {
        var batchNo = UUID.randomUUID()
                .toString();
        var startTime = System.currentTimeMillis();
        var aadIdsCount = Optional.ofNullable(aadIds)
                .map(List::size)
                .orElse(0);
        log.info("Starting user synchronization - batch: {}, users count: {}", batchNo, aadIdsCount);

        // Initialize result object
        var result = SyncReport.builder()
                .batchNo(batchNo)
                .requestedCount(aadIdsCount)
                .successfulAadIds(new ArrayList<>())
                .failedAadIds(new HashMap<>())
                .build();

        try {
            if (CollectionUtils.isEmpty(aadIds)) {
                log.warn("Batch: {}, No AAD IDs provided for synchronization", batchNo);
                return completeResultWithStatus(result, startTime, true, "No users to synchronize");
            }

            var userList = fetchUsersForSync(aadIds, batchNo);
            result.setFoundCount(userList.size());

            if (CollectionUtils.isEmpty(userList)) {
                log.warn("Batch: {}, No users found matching the synchronization criteria", batchNo);
                return completeResultWithStatus(result, startTime, true, "No matching users found");
            } else {
                var syncResults = processUserSynchronization(tenantId, batchNo, userList);
                convertAndUpdateResults(result, syncResults);
            }

            return completeResultWithStatus(result, startTime, true, null);
        } catch (Exception e) {
            log.error("Batch: {}, WeChat Work user synchronization failed: {}", batchNo, e.getMessage(), e);
            return completeResultWithStatus(result, startTime, false, e.getMessage());
        }
    }

    @Override
    public void statisticSyncResult(String tenantId, String batchNo) {
        // 查询调用企微接口同步的结果
        List<SyncLog> syncLogList = syncLogService.list(
                new LambdaQueryWrapper<SyncLog>().eq(SyncLog::getTenantId, tenantId)
                        .eq(SyncLog::getDeleted, JobConstant.DELETED_NO)
                        .eq(SyncLog::getBatchNo, batchNo)
                        .select(SyncLog::getUrl, SyncLog::getRequest, SyncLog::getResult,
                                SyncLog::getStatus));
        int successDept = 0;
        int failDept = 0;
        int successUser = 0;
        int failUser = 0;
        for (SyncLog syncLog : syncLogList) {
            if (JobConstant.DEPT_URL.contains(syncLog.getUrl())) {
                if (syncLog.getStatus()
                        .equals(WxCpErrorMsgEnum.CODE_0.getCode()
                                .toString())) {
                    successDept++;
                } else {
                    failDept++;
                }
            } else if (JobConstant.USER_URL.contains(syncLog.getUrl())) {
                if (syncLog.getStatus()
                        .equals(WxCpErrorMsgEnum.CODE_0.getCode()
                                .toString())) {
                    successUser++;
                } else {
                    failUser++;
                }
            }
        }
        log.info("同步结果:批次{}, 部门同步成功{}, 部门同步失败{}, 成员同步成功{}, 成员同步失败{}", batchNo,
                successDept, failDept, successUser, failUser);
        // 邮件或者机器人通知同步结果
        List<NotifyConfig> list = notifyConfigService.list(
                new LambdaQueryWrapper<NotifyConfig>().eq(NotifyConfig::getTenantId, tenantId)
                        .eq(NotifyConfig::getDeleted, JobConstant.DELETED_NO)
                        .eq(NotifyConfig::getIsOpen, 1));
        for (NotifyConfig notifyConfig : list) {
            if (notifyConfig.getNotifyType() == 2) {
                NotifyConfigTemplate notifyConfigTemplate = notifyConfigTemplateService.getOne(
                        new LambdaQueryWrapper<NotifyConfigTemplate>().eq(NotifyConfigTemplate::getTenantId, tenantId)
                                // .eq(NotifyConfigTemplate::getConfigId, notifyConfig.getId())
                                .eq(NotifyConfigTemplate::getDeleted,
                                        JobConstant.DELETED_NO)
                                .last("limit 1"));
                MailAccount mailAccount = new MailAccount();
                mailAccount.setHost(notifyConfig.getHost());
                mailAccount.setPort(notifyConfig.getPort()
                        .intValue());
                mailAccount.setSslEnable(notifyConfig.getSslEnable());
                mailAccount.setUser(notifyConfig.getUsername());
                mailAccount.setPass(notifyConfig.getPassword());
                mailAccount.setFrom(notifyConfig.getMail());
                if (Objects.nonNull(notifyConfig.getTimeOut())) {
                    mailAccount.setTimeout(notifyConfig.getTimeOut());
                }

                if (StringUtils.isEmpty(notifyConfig.getToUser())) {
                    log.error("邮件接收人为空: [{}],[{}]", tenantId, batchNo);
                    return;
                }

                String content = CharSequenceUtil.format(notifyConfigTemplate.getContent(), batchNo, successDept,
                        failDept,
                        successUser, failUser);
                content = content + ":\n\n" + new Gson().toJson(syncLogList);
                EmailSendCommandVo command = new EmailSendCommandVo();
                command.setSource(notifyConfig.getMail());
                command.setSendTos(List.of(notifyConfig.getToUser()
                        .split(",")));
                command.setSubject(notifyConfigTemplate.getTitle());
                command.setText(content);
                command.setHtml(false);
                RespWrapVo<EmailReplyVo> respWrapVo = messageClient.sendWithReply(command);
                log.info("邮件发送成功:[{}],[{}],[{}]", tenantId, batchNo, JSONUtil.toJsonStr(respWrapVo));
            }
        }
    }

    /**
     * Converts and updates the final results from individual user synchronization
     * results
     *
     * @param result      The final result object to update
     * @param syncResults Map of AAD IDs to their synchronization results
     */
    private void convertAndUpdateResults(SyncReport result, Map<String, UserSyncResultDTO> syncResults) {
        if (syncResults == null) {
            return;
        }

        int successCount = 0;
        int failedCount = 0;

        for (var entry : syncResults.entrySet()) {
            var aadId = entry.getKey();
            var syncResult = entry.getValue();

            if (syncResult.isSuccessful()) {
                result.getSuccessfulAadIds()
                        .add(aadId);
                successCount++;
            } else {
                result.getFailedAadIds()
                        .put(aadId, syncResult.getErrorMessage());
                failedCount++;
            }
        }

        result.setSuccessCount(successCount);
        result.setFailedCount(failedCount);
    }

    /**
     * Completes the result object with timing and status information
     *
     * @param result     The result object to update
     * @param startTime  Process start time in milliseconds
     * @param successful Whether the process completed successfully
     * @param message    Optional status message
     * @return The updated result object
     */
    private SyncReport completeResultWithStatus(SyncReport result, long startTime, boolean successful, String message) {
        var duration = System.currentTimeMillis() - startTime;
        result.setDurationMs(duration);
        result.setSuccessful(successful);
        result.setErrorMessage(message);

        if (successful) {
            log.info("Batch: {}, WeChat Work user synchronization completed, duration: {}ms, " +
                    "success: {}, failed: {}", result.getBatchNo(), duration, result.getSuccessCount(),
                    result.getFailedCount());
        } else {
            log.error("Batch: {}, WeChat Work user synchronization failed: {}, duration: {}ms", result.getBatchNo(),
                    message, duration);
        }

        return result;
    }

    /**
     * Processes user synchronization by partitioning users and handling each batch
     *
     * @param tenantId The tenant identifier
     * @param batchNo  Current batch number
     * @param userList List of users to synchronize
     * @return Map of AAD IDs to their synchronization results
     */
    private Map<String, UserSyncResultDTO> processUserSynchronization(String tenantId, String batchNo,
            List<User> userList) {
        var partition = Lists.partition(userList, 1000);
        log.info("Batch: {}, Starting user batch processing - total users: {}, batches: {}", batchNo, userList.size(),
                partition.size());

        var results = new HashMap<String, UserSyncResultDTO>();

        for (var i = 0; i < partition.size(); i++) {
            var batchUsers = partition.get(i);
            var batchResults = processSingleUserBatch(tenantId, batchNo, batchUsers, i + 1, partition.size());
            results.putAll(batchResults);
        }

        log.info("Batch: {}, User batch processing completed - success: {}, failed: {}", batchNo, results.values()
                .stream()
                .filter(UserSyncResultDTO::isSuccessful)
                .count(),
                results.values()
                        .stream()
                        .filter(r -> !r.isSuccessful())
                        .count());

        return results;
    }

    /**
     * Processes a single batch of users
     *
     * @param tenantId     The tenant identifier
     * @param batchNo      Current batch number
     * @param users        Users in this batch
     * @param batchIndex   Current batch index
     * @param totalBatches Total number of batches
     * @return Map of AAD IDs to their synchronization results
     */
    private Map<String, UserSyncResultDTO> processSingleUserBatch(String tenantId, String batchNo, List<User> users,
            int batchIndex, int totalBatches) {
        var results = new HashMap<String, UserSyncResultDTO>();

        try {
            var aadIds = users.stream()
                    .map(User::getAadId)
                    .distinct()
                    .toList();

            log.info("Batch: {}, Fetching department relations for AAD IDs: {}, tenantId: {}", batchNo, aadIds,
                    tenantId);
            var deptUserList = departmentUserService.list(
                    new LambdaQueryWrapper<DepartmentUser>().eq(DepartmentUser::getTenantId, tenantId)
                            .eq(DepartmentUser::getDeleted, JobConstant.DELETED_NO)
                            .in(DepartmentUser::getAadId, aadIds));

            log.info("Batch: {}, Processing batch {}/{} - users: {}, department relations: {}", batchNo, batchIndex,
                    totalBatches, users.size(), deptUserList.size());

            var userLoggedDTOList = Optional.ofNullable(userLoggedService.list())
                    .orElse(Collections.emptyList());
            var userLoggedAadIds = userLoggedDTOList.stream()
                    .map(UserLoggedDTO::getAadId)
                    .collect(Collectors.toSet());
            var loggedUserEmailMap = userLoggedDTOList.stream()
                    .collect(Collectors.toMap(UserLoggedDTO::getAadId,
                            UserLoggedDTO::getSourceUserid,
                            (email1, email2) -> email1));
            updateDirectLeadersForLoggedUsers(users, userLoggedAadIds, loggedUserEmailMap);
            // Call service to sync users and collect results
            results.putAll(userService.syncUserToWeCom(tenantId, users, deptUserList, batchNo));

            // If service doesn't return results, create default ones
            if (results.isEmpty()) {
                for (var user : users) {
                    results.put(user.getAadId(), UserSyncResultDTO.builder()
                            .successful(true)
                            .build());
                }
            }
        } catch (Exception e) {
            log.error("Batch: {}, Error processing batch {}/{}: {}", batchNo, batchIndex, totalBatches, e.getMessage(),
                    e);

            // Mark all users in this batch as failed
            for (var user : users) {
                results.put(user.getAadId(), UserSyncResultDTO.builder()
                        .successful(false)
                        .build());
            }
        }
        return results;
    }

    /**
     * Fetches users that need to be synchronized based on provided criteria
     *
     * @param aadIds  List of AAD IDs to synchronize
     * @param batchNo Current batch number
     * @return List of users that need synchronization
     */
    private List<User> fetchUsersForSync(List<String> aadIds, String batchNo) {
        log.info("Batch: {}, Building user query with {} AAD IDs", batchNo, aadIds.size());

        // 先将指定AAD ID的用户状态更新为初始状态, 确保可以重新同步
        userService.update(new LambdaUpdateWrapper<User>().in(User::getAadId, aadIds)
                .set(User::getSyncStatus, SyncStatusEnum.INIT.getCode())
                .set(User::getGmtModified, new Date()));

        log.info("Batch: {}, Updated sync status to INIT for {} AAD IDs", batchNo, aadIds.size());

        // 查询更新后的用户列表
        var queryWrapper = new LambdaQueryWrapper<User>().in(User::getAadId, aadIds);
        List<User> usersToBeHandle = userService.list(queryWrapper);

        log.info("Batch: {}, Found {} users to synchronize", batchNo, usersToBeHandle.size());

        return usersToBeHandle;
    }

    /**
     * 处理用户批次同步
     *
     * @param tenantId  租户ID
     * @param batchNo   批次号
     * @param partition 用户分批列表
     */
    void processUserBatches(String tenantId, String batchNo, List<List<User>> partition) {
        int batchIndex = 0;

        for (List<User> partitionUsers : partition) {
            batchIndex++;

            List<String> aadIds = partitionUsers.stream()
                    .map(User::getAadId)
                    .distinct()
                    .toList();

            List<DepartmentUser> deptUserList = departmentUserService.list(
                    new LambdaQueryWrapper<DepartmentUser>().eq(DepartmentUser::getTenantId, tenantId)
                            .eq(DepartmentUser::getDeleted, JobConstant.DELETED_NO)
                            .in(DepartmentUser::getAadId, aadIds));

            log.info("批量同步员工信息, 批次: {}, 批次序号: {}/{} - 用户数: {}, 关联部门关系数: {}", batchNo, batchIndex,
                    partition.size(), partitionUsers.size(), deptUserList.size());
            userService.syncUserToWeCom(tenantId, partitionUsers, deptUserList, batchNo);
        }
    }

    /**
     * Updates direct leader references for users whose leaders have already logged
     * in
     *
     * @param users              List of users to update
     * @param loggedUserIds      Set of AAD IDs for logged-in users
     * @param loggedUserEmailMap Mapping of AAD IDs to email addresses
     */
    void updateDirectLeadersForLoggedUsers(List<User> users, Set<String> loggedUserIds,
            Map<String, String> loggedUserEmailMap) {
        if (CollectionUtils.isEmpty(users) || CollectionUtils.isEmpty(loggedUserIds) || loggedUserEmailMap == null) {
            log.warn("Skip updating leaders - invalid input parameters");
            return;
        }

        users.forEach(user -> {
            try {
                Optional.ofNullable(user.getDirectLeader())
                        .filter(StringUtils::isNotEmpty)
                        .filter(loggedUserIds::contains)
                        .ifPresent(leaderAadId -> {
                            String leaderEmail = loggedUserEmailMap.get(leaderAadId);
                            if (StringUtils.isNotEmpty(leaderEmail)) {
                                user.setDirectLeader(leaderEmail);
                                log.info("Updated logged-in leader reference - User: {} (AAD ID: {}), Leader email: {}",
                                        user.getSourceUserid(), user.getAadId(), leaderEmail);
                            } else {
                                log.warn("Leader email not found for AAD ID: {} (User: {})", leaderAadId,
                                        user.getSourceUserid());
                            }
                        });
            } catch (Exception e) {
                log.error("Error updating leader for user: {} (AAD ID: {}) - {}", user.getSourceUserid(),
                        user.getAadId(), e.getMessage());
            }
        });
    }

    /**
     * Result class for user synchronization operations
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SyncReport {

        /**
         * Batch number for this synchronization operation
         */
        private String batchNo;

        /**
         * Total number of AAD IDs requested for synchronization
         */
        private int requestedCount;

        /**
         * Number of users found for synchronization
         */
        private int foundCount;

        /**
         * Number of users successfully synchronized
         */
        private int successCount;

        /**
         * Number of users that failed synchronization
         */
        private int failedCount;

        /**
         * Duration of the synchronization operation in milliseconds
         */
        private long durationMs;

        /**
         * Synchronization status
         */
        private boolean successful;

        /**
         * Error message if synchronization failed
         */
        private String errorMessage;

        /**
         * Error message if synchronization failed
         */
        private List<SyncLog> report;

        /**
         * List of AAD IDs that were successfully synchronized
         */
        private List<String> successfulAadIds;

        /**
         * Map of failed AAD IDs to their failure reasons
         */
        private Map<String, String> failedAadIds;

    }

}
