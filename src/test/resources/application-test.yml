server:
  port: 8000

logging:
  level:
    root: error
  config: classpath:log4j2.xml

#多实例配置
spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:db_qa_wecom_db;MODE=PostgreSQL;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
    username: test
    password:
  sql:
    init:
      schema-locations: classpath:dbschemainit/db_qa_wecom_initial_db.sql

## mybatis配置项
mybatis-plus:
  mapper-locations: classpath:/mapper/**/*Mapper.xml
  global-config:
    banner: false
    db-config:
      id-type: assign_id
  configuration:
    cache-enabled: true
    default-executor-type: reuse
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  entry-ttl: 8

application:
  services:
    unifiedMessaging: https://dev-kip-service-internal.kerryonvip.com/unified-messaging-service

wechat:
  cp:
    appConfigs:
      - agentId: 1000001
        corpId: ww4aaccf11cd9ae333
        secret: RJy01-sLJL7nlNK8wE1eb_or5oeG7Scpq4Bz4TTaXW8


distributed:
  jobs:
    enabled: true