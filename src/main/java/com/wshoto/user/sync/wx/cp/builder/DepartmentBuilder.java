package com.wshoto.user.sync.wx.cp.builder;

import cn.hutool.core.util.StrUtil;
import com.wshoto.user.sync.app.dao.entity.Department;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpDepart;

/**
 *  <AUTHOR>
 */
public class DepartmentBuilder{

  public static WxCpDepart build(Department department) {
    WxCpDepart wxCpDepart = new WxCpDepart();
    if(StrUtil.isNotEmpty(department.getTargetDeptId())){
      wxCpDepart.setId(Long.valueOf(department.getTargetDeptId()));
    }
    wxCpDepart.setName(department.getName());
    wxCpDepart.setEnName(department.getNameEn());
    if(department.getOrder() != null){
      wxCpDepart.setOrder(department.getOrder().longValue());
    }else {
      wxCpDepart.setOrder(0L);
    }
    if(StrUtil.isNotEmpty(department.getTargetDeptParentId())){
      wxCpDepart.setParentId(Long.valueOf(department.getTargetDeptParentId()));
    }
    return wxCpDepart;
  }

}
