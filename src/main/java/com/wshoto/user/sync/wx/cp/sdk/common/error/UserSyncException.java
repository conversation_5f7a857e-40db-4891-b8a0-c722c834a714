package com.wshoto.user.sync.wx.cp.sdk.common.error;

import com.wshoto.user.sync.wx.cp.sdk.common.enums.ExceptionType;

/**
 * WxJava专用的runtime exception.
 *
 * <AUTHOR>
 * created on  2020-09-26
 */

public class UserSyncException extends RuntimeException {
  private Integer code=-1;
  public UserSyncException(Throwable e) {
    super(e);
  }

  public UserSyncException(String msg) {
    super(msg);
  }
  public UserSyncException(ExceptionType exceptionType) {
    super(exceptionType.getDesc());
    this.code= exceptionType.getCode();
  }

  public UserSyncException(String msg, Throwable e) {
    super(msg, e);
  }

  public Integer getCode() {
    return code;
  }
}
