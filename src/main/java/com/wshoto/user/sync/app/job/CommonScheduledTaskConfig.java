package com.wshoto.user.sync.app.job;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.dao.entity.Job;
import com.wshoto.user.sync.app.service.JobService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@EnableScheduling
@Slf4j
@ConditionalOnProperty(value = "distributed.jobs.enabled", matchIfMissing= true)
public class CommonScheduledTaskConfig implements SchedulingConfigurer {
    @Resource
    private JobService jobService;
    @Resource
    private QwUserSyncJob qwUserSyncJob;

    @Override
    public void configureTasks(@NotNull ScheduledTaskRegistrar taskRegistrar) {

        List<Job> list = jobService.list(new LambdaQueryWrapper<Job>().eq(Job::getDeleted, 0));
        for (Job job : list) {
            log.info("企微员工同步任务job:{}", JSONUtil.toJsonStr(job));
            String tenantId = job.getTenantId();
            String cron = job.getCron();
            String jobId = job.getJobId();

            if(StrUtil.isEmpty(cron) || "-".equals(cron) || StrUtil.isEmpty(jobId)){
                log.info("企微员工同步任务执行取消");
                return;
            }

            log.info("企微员工同步任务执行开始");
            taskRegistrar.addTriggerTask(
                    () -> executeTask(tenantId, jobId)
                    , triggerContext -> Objects.requireNonNull(
                            new CronTrigger(cron).nextExecution(triggerContext)));
        }
    }

    private void executeTask(String tenantId, String jobId) {
        if(jobId.equals(JobConstant.QW_USER_SYNC_JOB_ID)){
            String batchNo = qwUserSyncJob.executeTask(tenantId);
            qwUserSyncJob.statisticSyncResult(tenantId,batchNo);
        }


    }
}
