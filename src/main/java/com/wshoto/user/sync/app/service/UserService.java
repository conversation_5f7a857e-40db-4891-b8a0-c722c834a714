package com.wshoto.user.sync.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wshoto.user.sync.app.dao.entity.DepartmentUser;
import com.wshoto.user.sync.app.dao.entity.User;
import com.wshoto.user.sync.app.common.dto.UserSyncResultDTO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 成员同步表 服务类
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
public interface UserService extends IService<User> {
    /**
     * Synchronizes users to WeChat Work
     *
     * @param tenantId Tenant identifier
     * @param part List of users to synchronize
     * @param departmentUserList List of department-user relationships
     * @param batchNo Batch number for this operation
     * @return Map of AAD IDs to their synchronization results
     */
    Map<String, UserSyncResultDTO> syncUserToWeCom(String tenantId, List<User> part, List<DepartmentUser> departmentUserList, String batchNo);

    /**
     * 更新成员同步状态为进行中
     * @param user
     */
    void updateSyncStatusProgressing(User user);

    /**
     * 更新成员同步状态为完成
     * @param user
     */
    void updateSyncStatusSuccess(User user);

    /**
     * 更新成员同步状态为失败
     * @param user
     */
    void updateSyncStatusFail(User user);

    /**
     * 删除成员成功
     * @param user
     */
    void deleteUserSuccess(User user);

    /**
     * 删除员工
     * @param tenantId
     * @param batchNo
     */
    void syncDeleteUser(String tenantId, String batchNo);
}
