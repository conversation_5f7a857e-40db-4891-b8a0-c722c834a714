package com.wshoto.user.sync.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wshoto.user.sync.app.common.config.WeComConversationFilter;
import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.common.dto.UserSyncResultDTO;
import com.wshoto.user.sync.app.common.enums.DataStatusEnum;
import com.wshoto.user.sync.app.common.enums.SyncStatusEnum;
import com.wshoto.user.sync.app.component.QwSyncComponent;
import com.wshoto.user.sync.app.dao.entity.DepartmentUser;
import com.wshoto.user.sync.app.dao.entity.User;
import com.wshoto.user.sync.app.dao.entity.UserLoggedDTO;
import com.wshoto.user.sync.app.dao.entity.UserSyncConfig;
import com.wshoto.user.sync.app.dao.mapper.UserMapper;
import com.wshoto.user.sync.app.service.UserLoggedService;
import com.wshoto.user.sync.app.service.UserService;
import com.wshoto.user.sync.app.service.UserSyncConfigService;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxRuntimeException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 成员同步表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Resource
    private QwSyncComponent qwSyncComponent;

    @Resource
    private UserSyncConfigService userSyncConfigService;

    @Resource
    private UserLoggedService userLoggedService;

    @Resource
    private UserMapper userMapper;

    @Override
    public Map<String, UserSyncResultDTO> syncUserToWeCom(String tenantId, List<User> userList,
                                                          List<DepartmentUser> departmentUserList, String batchNo) {
        var results = new HashMap<String, UserSyncResultDTO>();
        var userLoggedAadIds = userLoggedService.list()
                                                        .stream()
                                                        .map(UserLoggedDTO::getAadId)
                                                        .collect(Collectors.toSet());
        try {
            log.info("Batch: {}, Starting user synchronization to WeChat Work - users: {}, department relations: {}",
                     batchNo, userList.size(), departmentUserList.size());

            // 构建用户部门关系映射
            Map<String, List<DepartmentUser>> userDeptMap = departmentUserList.stream()
                                                                              .collect(Collectors.groupingBy(
                                                                                      DepartmentUser::getAadId));

            // 获取创建操作配置
            List<UserSyncConfig> createConfigList = userSyncConfigService.list(
                    new LambdaQueryWrapper<UserSyncConfig>().eq(UserSyncConfig::getTenantId, tenantId)
                                                            .eq(UserSyncConfig::getSyncType,
                                                                DataStatusEnum.CREATE.getCode())
                                                            .eq(UserSyncConfig::getDeleted, JobConstant.DELETED_NO));
            Map<String, UserSyncConfig> createConfigMap = createConfigList.stream()
                                                                          .collect(Collectors.toMap(
                                                                                  UserSyncConfig::getSyncColumn,
                                                                                  Function.identity(), (x1, x2) -> x1));
            log.info("Batch: {}, Create user configuration items: {}", batchNo, createConfigMap.size());

            // 获取更新操作配置
            List<UserSyncConfig> updateConfigList = userSyncConfigService.list(
                    new LambdaQueryWrapper<UserSyncConfig>().eq(UserSyncConfig::getTenantId, tenantId)
                                                            .eq(UserSyncConfig::getSyncType,
                                                                DataStatusEnum.UPDATE.getCode())
                                                            .eq(UserSyncConfig::getDeleted, JobConstant.DELETED_NO));
            Map<String, UserSyncConfig> updateConfigMap = updateConfigList.stream()
                                                                          .collect(Collectors.toMap(
                                                                                  UserSyncConfig::getSyncColumn,
                                                                                  Function.identity(), (x1, x2) -> x1));
            log.info("Batch: {}, Update user configuration items: {}", batchNo, updateConfigMap.size());

            // 获取用户字段映射
            Map<String, Method> userFieldMethodMap = getUserField(User.class);

            // 统计各操作用户数量
            int createCount = 0;
            int updateCount = 0;
            int deleteCount = 0;
            int skipCount = 0;

            // 处理每个用户
            for (User user : userList) {
                WeComConversationFilter.refreshCorrelationId();
                log.info("Batch: {}, Processing user - name: {}, AAD ID: {}, target user ID: {}", batchNo,
                         user.getName(), user.getAadId(), user.getTargetUserid());
                if (!SyncStatusEnum.INIT.getCode()
                                        .equals(user.getSyncStatus())) {
                    log.info("Batch: {}, Skipping user - {}", batchNo, user);
                    skipCount++;
                    results.put(user.getAadId(), UserSyncResultDTO.builder()
                                                                  .successful(true)
                                                                  .errorMessage("Skipped: User sync status is not INIT")
                                                                  .build());
                    continue;
                }

                List<DepartmentUser> departmentUserList1 = userDeptMap.get(user.getAadId());

                // Update AAD ID for disabled logged users
                updateAadIdForDisabledLoggedUsers(batchNo, userList, userLoggedAadIds);

                int deptRelationCount = departmentUserList1 != null ? departmentUserList1.size() : 0;

                try {
                    if (DataStatusEnum.CREATE.getCode()
                                             .equals(user.getDataStatus())) {
                        log.info("Batch: {}, Creating user - name: {}, AAD ID: {}, department relations: {}", batchNo,
                                 user.getName(), user.getAadId(), deptRelationCount);
                        qwSyncComponent.createUser(user, departmentUserList1, batchNo, createConfigMap,
                                                   userFieldMethodMap);
                        createCount++;
                        results.put(user.getAadId(), UserSyncResultDTO.builder()
                                                                      .successful(true)
                                                                      .build());
                    } else if (DataStatusEnum.UPDATE.getCode()
                                                    .equals(user.getDataStatus())) {
                        log.info("Batch: {}, Updating user - name: {}, AAD ID: {}, target user ID: {}, department " +
                                         "relations: {}", batchNo, user.getName(), user.getAadId(),
                                 user.getTargetUserid(), deptRelationCount);
                        qwSyncComponent.updateUser(user, departmentUserList1, batchNo, updateConfigMap,
                                                   userFieldMethodMap);
                        updateCount++;
                        results.put(user.getAadId(), UserSyncResultDTO.builder()
                                                                      .successful(true)
                                                                      .build());
                    } else if (DataStatusEnum.DELETE.getCode()
                                                    .equals(user.getDataStatus())) {
                        log.info("Batch: {}, Deleting user - name: {}, AAD ID: {}, target user ID: {}", batchNo,
                                 user.getName(), user.getAadId(), user.getTargetUserid());
                        qwSyncComponent.deleteUser(user, departmentUserList1, batchNo);
                        deleteCount++;
                        results.put(user.getAadId(), UserSyncResultDTO.builder()
                                                                      .successful(true)
                                                                      .build());
                    }
                } catch (Exception e) {
                    log.error("Batch: {}, Error processing user - name: {}, AAD ID: {}, error: {}", batchNo,
                              user.getName(), user.getAadId(), e.getMessage(), e);
                    results.put(user.getAadId(), UserSyncResultDTO.builder()
                                                                  .successful(false)
                                                                  .errorMessage(e.getMessage())
                                                                  .build());
                }
            }

            log.info("Batch: {}, User synchronization completed - total: {}, created: {}, updated: {}, deleted: {}, " +
                             "skipped: {}", batchNo, userList.size(), createCount, updateCount, deleteCount, skipCount);
        } catch (Exception e) {
            log.error("Batch: {}, User synchronization error: {}", batchNo, e.getMessage(), e);
            // Mark all users as failed if we have a global exception
            for (User user : userList) {
                results.put(user.getAadId(), UserSyncResultDTO.builder()
                                                              .successful(false)
                                                              .errorMessage(
                                                                      "Global synchronization error: " + e.getMessage())
                                                              .build());
            }
        }

        return results;
    }

    private static void updateAadIdForDisabledLoggedUsers(String batchNo, List<User> userList, Set<String> userLoggedAadIds) {
        userList.forEach(stuff -> {
            if (stuff.getEnable() == 0 && userLoggedAadIds.contains(stuff.getAadId())) {
                stuff.setAadId(stuff.getSourceUserid());
                log.info("批次: {}, AAD ID: {}, 即将禁用已登录用户: {}, 更新AAD ID为sourceUserId", batchNo,
                         stuff.getAadId(), stuff.getSourceUserid());
            }
        });
    }

    @Override
    public void updateSyncStatusProgressing(User user) {
        userMapper.update(new LambdaUpdateWrapper<User>().eq(User::getId, user.getId())
                                                         .eq(User::getTenantId, user.getTenantId())
                                                         .eq(User::getDeleted, JobConstant.DELETED_NO)
                                                         .eq(User::getSyncStatus, SyncStatusEnum.INIT.getCode())
                                                         .set(User::getSyncStatus, SyncStatusEnum.PROGRESSING.getCode())
                                                         .set(User::getGmtModified, new Date()));
    }

    @Override
    public void updateSyncStatusSuccess(User user) {
        userMapper.update(new LambdaUpdateWrapper<User>().eq(User::getId, user.getId())
                                                         .eq(User::getTenantId, user.getTenantId())
                                                         .eq(User::getDeleted, JobConstant.DELETED_NO)
                                                         .set(User::getSyncStatus, SyncStatusEnum.SUCCESS.getCode())
                                                         .set(User::getGmtModified, new Date()));
    }

    @Override
    public void updateSyncStatusFail(User user) {
        userMapper.update(new LambdaUpdateWrapper<User>().eq(User::getTenantId, user.getTenantId())
                                                         .eq(User::getDeleted, JobConstant.DELETED_NO)
                                                         .eq(User::getId, user.getId())
                                                         .set(User::getSyncStatus, SyncStatusEnum.FAIL.getCode())
                                                         .set(User::getGmtModified, new Date()));
    }

    @Override
    public void deleteUserSuccess(User user) {
        userMapper.update(new LambdaUpdateWrapper<User>().eq(User::getTenantId, user.getTenantId())
                                                         .eq(User::getDeleted, JobConstant.DELETED_NO)
                                                         .eq(User::getId, user.getId())
                                                         .set(User::getDeleted, JobConstant.DELETED_YES)
                                                         .set(User::getSyncStatus, SyncStatusEnum.SUCCESS.getCode())
                                                         .set(User::getGmtModified, new Date()));
    }

    @Override
    public void syncDeleteUser(String tenantId, String batchNo) {
        try {
            WeComConversationFilter.refreshCorrelationId();

            List<User> userDeleteList = list(new LambdaQueryWrapper<User>().eq(User::getTenantId, tenantId)
                                                                           .eq(User::getDeleted, JobConstant.DELETED_NO)
                                                                           .eq(User::getSyncStatus,
                                                                               SyncStatusEnum.INIT.getCode())
                                                                           .eq(User::getDataStatus,
                                                                               DataStatusEnum.DELETE.getCode()));

            if (CollectionUtils.isEmpty(userDeleteList)) {
                log.info("Batch: {}, No users to delete", batchNo);
                return;
            }

            log.info("Batch: {}, Users to delete: {}", batchNo, userDeleteList.size());

            List<UserLoggedDTO> userLoggedDTOList = userLoggedService.list();
            Set<String> userLoggedAadIds = CollectionUtils.isEmpty(userLoggedDTOList) ? Collections.emptySet() :
                    userLoggedDTOList.stream()
                                     .map(UserLoggedDTO::getAadId)
                                     .collect(Collectors.toSet());

            int oldMannerCount = 0;
            int normalCount = 0;

            for (User user : userDeleteList) {
                if (userLoggedAadIds.contains(user.getAadId())) {
                    qwSyncComponent.deleteUserOldManner(user, batchNo);
                    oldMannerCount++;
                } else {
                    qwSyncComponent.deleteUser(user, new ArrayList<>(), batchNo);
                    normalCount++;
                }
            }

            log.info("Batch: {}, User deletion completed - total: {}, old manner: {}, normal manner: {}", batchNo,
                     userDeleteList.size(), oldMannerCount, normalCount);
        } catch (Exception e) {
            log.error("Batch: {}, User deletion error: {}", batchNo, e.getMessage(), e);
            throw e;
        }
    }

    private Map<String, Method> getUserField(Class<User> userClass) {
        Field[] declaredFields = userClass.getDeclaredFields();
        Map<String, Method> annotation2MethodMap = new HashMap<>();
        for (Field declaredField : declaredFields) {
            JsonProperty annotation = declaredField.getAnnotation(JsonProperty.class);
            if (null == annotation) {
                continue;
            }
            try {
                String fieldName = declaredField.getName();
                PropertyDescriptor pd = new PropertyDescriptor(fieldName, userClass);
                //获得get方法
                Method getMethod = pd.getReadMethod();
                annotation2MethodMap.put(annotation.value(), getMethod);
            } catch (Exception e) {
                throw new WxRuntimeException(e);
            }
        }
        return annotation2MethodMap;
    }

}
