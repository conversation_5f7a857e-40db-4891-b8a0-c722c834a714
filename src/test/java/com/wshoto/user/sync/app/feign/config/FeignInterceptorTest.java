package com.wshoto.user.sync.app.feign.config;

import feign.RequestTemplate;
import org.apache.logging.log4j.ThreadContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.wshoto.user.sync.app.common.config.WeComConversationFilter.CONVERSATION_ID_HEADER;
import static com.wshoto.user.sync.app.common.config.WeComConversationFilter.MDC_CONVERSATION_ID_KEY;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class FeignInterceptorTest {

    @Test
    @DisplayName("当ThreadContext有ConversationId时，添加Header")
    void apply_ThreadContextHasConversationId_HeaderAdded() {
        // Arrange
        String mockConversationId = "test-conversation-id";
        ThreadContext.put(MDC_CONVERSATION_ID_KEY, mockConversationId);
        FeignInterceptor feignInterceptor = new FeignInterceptor();
        RequestTemplate requestTemplate = new RequestTemplate();

        // Act
        feignInterceptor.apply(requestTemplate);

        // Assert
        assertThat(requestTemplate.headers()).containsEntry(CONVERSATION_ID_HEADER,
                                                            java.util.Collections.singletonList(mockConversationId));

        // Cleanup
        ThreadContext.remove(MDC_CONVERSATION_ID_KEY);
    }

    @Test
    @DisplayName("当ThreadContext没有ConversationId时，不添加Header")
    void apply_ThreadContextHasNoConversationId_NoHeaderAdded() {
        // Arrange
        ThreadContext.remove(MDC_CONVERSATION_ID_KEY);
        FeignInterceptor feignInterceptor = new FeignInterceptor();
        RequestTemplate requestTemplate = new RequestTemplate();

        // Act
        feignInterceptor.apply(requestTemplate);

        // Assert
        assertThat(requestTemplate.headers()).doesNotContainKey(CONVERSATION_ID_HEADER);
    }

}