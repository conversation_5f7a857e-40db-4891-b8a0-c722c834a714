package com.wshoto.user.sync.wx.cp.error;

import com.wshoto.user.sync.app.BaseIntegrationTest;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;

/**
 * ErrorControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Yu 2025-04-23 14:17:38
 **/
class ErrorControllerIntegrationTest extends BaseIntegrationTest {

    @Test
    @DisplayName("error404-正常响应")
    void error404_success() {
        given().when()
               .get("/error/404")
               .then()
               .statusCode(200)
               .contentType(ContentType.HTML);
    }

    @Test
    @DisplayName("error500-正常响应")
    void error500_success() {
        given().when()
               .get("/error/500")
               .then()
               .statusCode(200)
               .contentType(ContentType.HTML);
    }

}