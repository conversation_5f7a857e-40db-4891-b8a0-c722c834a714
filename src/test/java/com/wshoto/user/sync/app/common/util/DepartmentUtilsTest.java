package com.wshoto.user.sync.app.common.util;

import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.dao.entity.Department;
import org.instancio.Instancio;
import org.instancio.Select;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class DepartmentUtilsTest {

    @Test
    @DisplayName("生成树形结构-含有无效父节点的部门-未包含无效父节点子部门")
    void generateTree_invalidParentIds_excludeInvalidNodes() {
        // Arrange
        Department root = Instancio.of(Department.class)
                                   .set(Select.field(Department::getSourceDeptId), "ROOT")
                                   .set(Select.field(Department::getTargetDeptId), JobConstant.ROOT_DEPT)
                                   .set(Select.field(Department::getName), "Root Department")
                                   .create();

        Department validChild = Instancio.of(Department.class)
                                         .set(Select.field(Department::getSourceDeptId), "CHILD_1")
                                         .set(Select.field(Department::getSourceDeptParentId), "ROOT")
                                         .set(Select.field(Department::getTargetDeptId), "CHILD_1_TARGET")
                                         .set(Select.field(Department::getName), "Valid Child")
                                         .create();

        Department invalidChild = Instancio.of(Department.class)
                                           .set(Select.field(Department::getSourceDeptId), "CHILD_2")
                                           .set(Select.field(Department::getSourceDeptParentId), "INVALID_PARENT")
                                           .set(Select.field(Department::getTargetDeptId), "CHILD_2_TARGET")
                                           .set(Select.field(Department::getName), "Invalid Child")
                                           .create();

        List<Department> inputDepartments = List.of(root, validChild, invalidChild);

        // Act
        List<Department> result = new DepartmentUtils().generateTree(inputDepartments);

        // Assert
        assertThat(result).isNotNull()
                          .hasSize(2)
                          .containsExactlyInAnyOrder(root, validChild);
    }

    @Test
    @DisplayName("生成树形结构-循环依赖-正常处理")
    void generateTree_circularDependency_ok() {
        // Arrange
        Department root = Instancio.of(Department.class)
                                   .set(Select.field(Department::getSourceDeptId), "ROOT")
                                   .set(Select.field(Department::getTargetDeptId), JobConstant.ROOT_DEPT)
                                   .set(Select.field(Department::getName), "Root Department")
                                   .create();

        Department child1 = Instancio.of(Department.class)
                                     .set(Select.field(Department::getSourceDeptId), "CHILD_1")
                                     .set(Select.field(Department::getSourceDeptParentId), "ROOT")
                                     .set(Select.field(Department::getTargetDeptId), "CHILD_1_TARGET")
                                     .set(Select.field(Department::getName), "Child 1")
                                     .create();

        // Circular dependency: child1 points back to the root, and the root points to child1
        root.setSourceDeptParentId(child1.getSourceDeptId());

        List<Department> inputDepartments = List.of(root, child1);

        var departments = new DepartmentUtils().generateTree(inputDepartments);

        assertThat(departments).hasSize(2);
    }

    @Test
    @DisplayName("生成树形结构-无根部门-结果为空")
    void generateTree_noRoot_emptyResult() {
        Department orphan = Instancio.of(Department.class)
                                     .set(Select.field(Department::getSourceDeptId), "ORPHAN")
                                     .set(Select.field(Department::getSourceDeptParentId), "NON_EXISTENT")
                                     .set(Select.field(Department::getTargetDeptId), "ORPHAN_TARGET")
                                     .set(Select.field(Department::getName), "Orphan Department")
                                     .create();

        List<Department> inputDepartments = List.of(orphan);

        // Act & Assert
        List<Department> departments = new DepartmentUtils().generateTree(inputDepartments);

        assertThat(departments).isEmpty();
    }

    @Test
    @DisplayName("生成树形结构-根节点无子节点-结果仅包含根节点")
    void generateTree_rootWithNoChildren_emptyResult() {
        // Arrange
        Department root = Instancio.of(Department.class)
                                   .set(Select.field(Department::getSourceDeptId), "ROOT")
                                   .set(Select.field(Department::getTargetDeptId), JobConstant.ROOT_DEPT)
                                   .set(Select.field(Department::getName), "Root Department")
                                   .create();

        List<Department> inputDepartments = List.of(root);

        // Act
        List<Department> result = new DepartmentUtils().generateTree(inputDepartments);

        // Assert
        assertThat(result).isNotNull()
                          .hasSize(1)
                          .containsExactly(root);
    }

    @Test
    @DisplayName("生成树形结构-多级部门结构-正确生成层次树")
    void generateTree_multipleLevelsOfHierarchy_correctTree() {
        // Arrange
        Department root = Instancio.of(Department.class)
                                   .set(Select.field(Department::getSourceDeptId), "ROOT")
                                   .set(Select.field(Department::getTargetDeptId), JobConstant.ROOT_DEPT)
                                   .set(Select.field(Department::getName), "Root Department")
                                   .create();

        Department child1 = Instancio.of(Department.class)
                                     .set(Select.field(Department::getSourceDeptId), "CHILD_1")
                                     .set(Select.field(Department::getSourceDeptParentId), "ROOT")
                                     .set(Select.field(Department::getTargetDeptId), "CHILD_1_TARGET")
                                     .set(Select.field(Department::getName), "Child 1")
                                     .create();

        Department child11 = Instancio.of(Department.class)
                                      .set(Select.field(Department::getSourceDeptId), "CHILD_1_1")
                                      .set(Select.field(Department::getSourceDeptParentId), "CHILD_1")
                                      .set(Select.field(Department::getTargetDeptId), "CHILD_1_1_TARGET")
                                      .set(Select.field(Department::getName), "Child 1.1")
                                      .create();

        Department child2 = Instancio.of(Department.class)
                                     .set(Select.field(Department::getSourceDeptId), "CHILD_2")
                                     .set(Select.field(Department::getSourceDeptParentId), "ROOT")
                                     .set(Select.field(Department::getTargetDeptId), "CHILD_2_TARGET")
                                     .set(Select.field(Department::getName), "Child 2")
                                     .create();

        List<Department> inputDepartments = List.of(root, child1, child11, child2);

        // Act
        List<Department> result = new DepartmentUtils().generateTree(inputDepartments);

        // Assert
        assertThat(result).isNotNull()
                          .hasSize(4)
                          .containsExactlyInAnyOrder(root, child1, child11, child2);
    }

    @Test
    @DisplayName("生成树形结构-空部门列表-结果为空")
    void generateTree_emptyDepartmentList_emptyTree() {
        List<Department> inputDepartments = List.of();

        List<Department> departments = new DepartmentUtils().generateTree(inputDepartments);

        assertThat(departments).isEmpty();
    }

    @Test
    @DisplayName("生成树形结构-多个部门同ID-保留第一个遇到的部门")
    void generateTree_duplicateDepartmentIds_keepFirstEncountered() {
        // Arrange
        Department department1 = Instancio.of(Department.class)
                                          .set(Select.field(Department::getTargetDeptId), JobConstant.ROOT_DEPT)
                                          .set(Select.field(Department::getSourceDeptId), "DUPLICATE_ID")
                                          .set(Select.field(Department::getName), "First Department")
                                          .create();

        Department department2 = Instancio.of(Department.class)
                                          .set(Select.field(Department::getTargetDeptId), JobConstant.ROOT_DEPT)
                                          .set(Select.field(Department::getSourceDeptId), "DUPLICATE_ID")
                                          .set(Select.field(Department::getName), "Second Department")
                                          .create();

        List<Department> inputDepartments = List.of(department1, department2);

        // Act
        List<Department> result = new DepartmentUtils().generateTree(inputDepartments);

        // Assert
        assertThat(result).isNotNull()
                          .hasSize(1)
                          .containsExactly(department1);
    }

    @Test
    @DisplayName("生成树形结构-多个根部门-根据JobConstant选择正确的根节点")
    void generateTree_multipleRoots_selectCorrectRoot() {
        // Arrange
        Department invalidRoot = Instancio.of(Department.class)
                                          .set(Select.field(Department::getSourceDeptId), "INVALID_ROOT")
                                          .set(Select.field(Department::getTargetDeptId), "INVALID")
                                          .create();

        Department validRoot = Instancio.of(Department.class)
                                        .set(Select.field(Department::getSourceDeptId), "VALID_ROOT")
                                        .set(Select.field(Department::getTargetDeptId), JobConstant.ROOT_DEPT)
                                        .create();

        List<Department> inputDepartments = List.of(invalidRoot, validRoot);

        // Act
        List<Department> result = new DepartmentUtils().generateTree(inputDepartments);

        // Assert
        assertThat(result).isNotNull()
                          .hasSize(1)
                          .containsExactly(validRoot);
    }

    @Test
    @DisplayName("生成树形结构-无子部门的根部门-结果包含根部门")
    void generateTree_rootWithoutChildren_containsOnlyRoot() {
        // Arrange
        Department root = Instancio.of(Department.class)
                                   .set(Select.field(Department::getSourceDeptId), "ROOT")
                                   .set(Select.field(Department::getTargetDeptId), JobConstant.ROOT_DEPT)
                                   .set(Select.field(Department::getName), "Root Department")
                                   .create();

        List<Department> inputDepartments = List.of(root);

        // Act
        List<Department> result = new DepartmentUtils().generateTree(inputDepartments);

        // Assert
        assertThat(result).isNotNull()
                          .hasSize(1)
                          .containsExactly(root);
    }

    @Test
    @DisplayName("生成树形结构-带循环的部门结构-仅生成有效树")
    void generateTree_departmentStructureWithLoops_validTree() {
        // Arrange
        Department root = Instancio.of(Department.class)
                                   .set(Select.field(Department::getSourceDeptId), "ROOT")
                                   .set(Select.field(Department::getTargetDeptId), JobConstant.ROOT_DEPT)
                                   .set(Select.field(Department::getName), "Root Department")
                                   .create();

        Department child = Instancio.of(Department.class)
                                    .set(Select.field(Department::getSourceDeptId), "CHILD")
                                    .set(Select.field(Department::getSourceDeptParentId), "ROOT")
                                    .set(Select.field(Department::getTargetDeptId), "CHILD_TARGET")
                                    .set(Select.field(Department::getName), "Child Department")
                                    .create();

        // Create a loop where a child references the root as its parent
        Department loop = Instancio.of(Department.class)
                                   .set(Select.field(Department::getSourceDeptId), "LOOP")
                                   .set(Select.field(Department::getSourceDeptParentId), "CHILD")
                                   .set(Select.field(Department::getTargetDeptId), "LOOP_TARGET")
                                   .set(Select.field(Department::getName), "Loop Department")
                                   .create();

        child.setSourceDeptParentId(loop.getSourceDeptId());

        List<Department> inputDepartments = List.of(root, child, loop);

        // Act
        List<Department> result = new DepartmentUtils().generateTree(inputDepartments);

        // Assert
        assertThat(result).isNotNull()
                          .hasSize(1);
    }

    @Test
    @DisplayName("生成树形结构-null输入-返回空数组")
    void generateTree_nullInput_isEmpty() {
        // Act & Assert
        List<Department> departments = new DepartmentUtils().generateTree(null);

        assertThat(departments).isEmpty();
    }

}