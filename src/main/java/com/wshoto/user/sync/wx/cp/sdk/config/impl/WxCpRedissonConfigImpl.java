package com.wshoto.user.sync.wx.cp.sdk.config.impl;

import com.wshoto.user.sync.wx.cp.sdk.common.redis.RedissonWxRedisOps;
import lombok.NonNull;
import lombok.ToString;
import org.redisson.api.RedissonClient;

/**
 * 基于Redisson的实现
 *
 * <AUTHOR>  created on  2020 /5/13
 * <AUTHOR>
 */
@ToString
public class WxCpRedissonConfigImpl extends AbstractWxCpInRedisConfigImpl {
  private static final long serialVersionUID = -5674792341070783967L;

  public WxCpRedissonConfigImpl(@NonNull RedissonClient redissonClient) {
    this(redissonClient, null);
  }

  public WxCpRedissonConfigImpl(@NonNull RedissonClient redissonClient, String keyPrefix) {
    super(new RedissonWxRedisOps(redissonClient), keyPrefix);
  }
}
