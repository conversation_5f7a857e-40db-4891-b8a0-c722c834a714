package com.wshoto.user.sync.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.wshoto.user.sync.app.dao.entity.Department;
import com.wshoto.user.sync.app.dao.entity.DepartmentUser;
import com.wshoto.user.sync.app.dao.mapper.DepartmentUserMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.ibatis.session.Configuration;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.instancio.Select.field;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SuppressWarnings("ALL")
@ExtendWith(MockitoExtension.class)
class DepartmentUserServiceImplTest {

    @Mock
    private DepartmentUserMapper departmentUserMapper;

    @InjectMocks
    private DepartmentUserServiceImpl departmentUserService;

    @BeforeAll
    static void setUp() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new Configuration(), ""), DepartmentUser.class);
    }

    @Test
    @DisplayName("updateTargetDeptId - 有效参数 - 成功更新")
    void updateTargetDeptId_validInput_successUpdate() {
        // Arrange
        String tenantId = "tenant123";
        String sourceDeptId = "sourceDept1";
        String targetDeptId = "targetDept1";

        when(departmentUserMapper.update(any(LambdaUpdateWrapper.class))).thenReturn(1);

        // Act
        departmentUserService.updateTargetDeptId(tenantId, sourceDeptId, targetDeptId);

        // Assert
        verify(departmentUserMapper, times(1)).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    @DisplayName("deleteDepartmentUser - 有效参数 - 成功删除")
    void deleteDepartmentUser_validInput_successDelete() {
        // Arrange
        Department department = Instancio.of(Department.class)
                                         .set(field(Department::getTenantId), "tenant123")
                                         .set(field(Department::getSourceDeptId), "sourceDept1")
                                         .set(field(Department::getTargetDeptId), "targetDept1")
                                         .create();

        when(departmentUserMapper.update(any(LambdaUpdateWrapper.class))).thenReturn(1);

        // Act
        departmentUserService.deleteDepartmentUser(department);

        // Assert
        verify(departmentUserMapper, times(1)).update(any(LambdaUpdateWrapper.class));
    }

}