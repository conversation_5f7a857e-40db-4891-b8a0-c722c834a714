package com.wshoto.user.sync.wx.cp.sdk.api;

import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpTag;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpTagAddOrRemoveUsersResult;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpTagGetResult;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpUser;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxErrorException;

import java.util.List;

/**
 * <pre>
 *  标签管理接口.
 *  Created by BinaryWang on 2017/6/24.
 * </pre>
 *
 * <AUTHOR>
 */
public interface WxCpTagService {
  /**
   * 创建标签.
   * <pre>
   * 请求地址：https://qyapi.weixin.qq.com/cgi-bin/tag/create?access_token=ACCESS_TOKEN
   * 文档地址：https://work.weixin.qq.com/api/doc#90000/90135/90210
   * </pre>
   *
   * @param name 标签名称，长度限制为32个字以内（汉字或英文字母），标签名不可与其他标签重名。
   * @param id   标签id，非负整型，指定此参数时新增的标签会生成对应的标签id，不指定时则以目前最大的id自增。
   * @return 标签id string
   * @throws WxErrorException .
   */
  String create(String name, Integer id) throws WxErrorException;

  /**
   * 更新标签.
   *
   * @param tagId   标签id
   * @param tagName 标签名
   * @throws WxErrorException .
   */
  void update(String tagId, String tagName) throws WxErrorException;

  /**
   * 删除标签.
   *
   * @param tagId 标签id
   * @throws WxErrorException .
   */
  void delete(String tagId) throws WxErrorException;

  /**
   * 获得标签列表.
   *
   * @return 标签列表 list
   * @throws WxErrorException .
   */
  List<WxCpTag> listAll() throws WxErrorException;

  /**
   * 获取标签成员.
   *
   * @param tagId 标签ID
   * @return 成员列表 list
   * @throws WxErrorException .
   */
  List<WxCpUser> listUsersByTagId(String tagId) throws WxErrorException;

  /**
   * 获取标签成员.
   * 对应: http://qydev.weixin.qq.com/wiki/index.php?title=管理标签 中的get接口
   *
   * @param tagId 标签id
   * @return . wx cp tag get result
   * @throws WxErrorException .
   */
  WxCpTagGetResult get(String tagId) throws WxErrorException;

  /**
   * 增加标签成员.
   *
   * @param tagId    标签id
   * @param userIds  用户ID 列表
   * @param partyIds 企业部门ID列表
   * @return . wx cp tag add or remove users result
   * @throws WxErrorException .
   */
  WxCpTagAddOrRemoveUsersResult addUsers2Tag(String tagId, List<String> userIds, List<String> partyIds) throws WxErrorException;

  /**
   * 移除标签成员.
   *
   * @param tagId    标签id
   * @param userIds  用户id列表
   * @param partyIds 企业部门ID列表
   * @return . wx cp tag add or remove users result
   * @throws WxErrorException .
   */
  WxCpTagAddOrRemoveUsersResult removeUsersFromTag(String tagId, List<String> userIds, List<String> partyIds) throws WxErrorException;

}
