-- PostgreSQL建表语句

-- 1. 成员同步表 (us_user)
CREATE TABLE us_user (
    id BIGINT PRIMARY KEY,
    aad_id VARCHAR(255),
    tenant_id VARCHAR(255),
    source_userid VARCHAR(255),
    name VA<PERSON>HA<PERSON>(255),
    alias VARCHAR(255),
    mobile VARCHAR(255),
    position VARCHAR(255),
    gender VARCHAR(10),
    email VARCHAR(255),
    biz_mail VARCHAR(255),
    telephone VARCHAR(255),
    direct_leader VARCHAR(255),
    avatar_mediaid VARCHAR(255),
    enable INTEGER,
    extattr TEXT,
    to_invite INTEGER,
    external_position VARCHAR(255),
    external_profile TEXT,
    address VARCHAR(128),
    main_department INTEGER,
    data_status INTEGER,
    sync_status INTEGER,
    version INTEGER,
    deleted INTEGER,
    gmt_create TIMESTAMP,
    gmt_modified TIMESTAMP,
    target_userid VARCHAR(255)
);

COMMENT ON TABLE us_user IS '成员同步表';
COMMENT ON COLUMN us_user.id IS '主键';
COMMENT ON COLUMN us_user.aad_id IS '成员microsoft AAD系统提供的uuid';
COMMENT ON COLUMN us_user.tenant_id IS '租户id';
COMMENT ON COLUMN us_user.source_userid IS '成员userid';
COMMENT ON COLUMN us_user.name IS '成员名称';
COMMENT ON COLUMN us_user.alias IS '成员别名';
COMMENT ON COLUMN us_user.mobile IS '手机号码';
COMMENT ON COLUMN us_user.position IS '职务信息';
COMMENT ON COLUMN us_user.gender IS '性别。1表示男性，2表示女性';
COMMENT ON COLUMN us_user.email IS '邮箱';
COMMENT ON COLUMN us_user.biz_mail IS '企业邮箱';
COMMENT ON COLUMN us_user.telephone IS '座机';
COMMENT ON COLUMN us_user.direct_leader IS '直属上级';
COMMENT ON COLUMN us_user.avatar_mediaid IS '成员头像的mediaid';
COMMENT ON COLUMN us_user.enable IS '启用/禁用成员。1表示启用成员，0表示禁用成员';
COMMENT ON COLUMN us_user.extattr IS '自定义字段';
COMMENT ON COLUMN us_user.to_invite IS '是否邀请该成员使用企业微信，默认值为true';
COMMENT ON COLUMN us_user.external_position IS '对外职务';
COMMENT ON COLUMN us_user.external_profile IS '成员对外属性';
COMMENT ON COLUMN us_user.address IS '地址。长度最大128个字符';
COMMENT ON COLUMN us_user.main_department IS '主部门';
COMMENT ON COLUMN us_user.data_status IS '数据状态  1新增、2修改 、3删除';
COMMENT ON COLUMN us_user.sync_status IS '同步状态  0初始值 1同步中 2同步成功 3同步失败';
COMMENT ON COLUMN us_user.version IS '当前版本';
COMMENT ON COLUMN us_user.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN us_user.gmt_create IS '创建时间';
COMMENT ON COLUMN us_user.gmt_modified IS '修改时间';
COMMENT ON COLUMN us_user.target_userid IS '企微成员userid';

-- 2. 部门成员关系表 (us_department_user)
CREATE TABLE us_department_user (
    id BIGINT PRIMARY KEY,
    aad_id VARCHAR(255),
    tenant_id VARCHAR(255),
    source_dept_id VARCHAR(255),
    target_dept_id VARCHAR(255),
    tartget_userid VARCHAR(255),
    is_dept_leader INTEGER,
    dept_order INTEGER,
    version INTEGER,
    deleted INTEGER,
    gmt_create TIMESTAMP,
    gmt_modified TIMESTAMP
);

COMMENT ON TABLE us_department_user IS '部门成员关系数据';
COMMENT ON COLUMN us_department_user.id IS '主键';
COMMENT ON COLUMN us_department_user.aad_id IS '成员microsoft AAD系统提供的uuid';
COMMENT ON COLUMN us_department_user.tenant_id IS '租户id';
COMMENT ON COLUMN us_department_user.source_dept_id IS '来源部门id';
COMMENT ON COLUMN us_department_user.target_dept_id IS '目标系统(企微)部门id';
COMMENT ON COLUMN us_department_user.tartget_userid IS '目标系统成员id';
COMMENT ON COLUMN us_department_user.is_dept_leader IS '是否部门主管 1是 0否';
COMMENT ON COLUMN us_department_user.dept_order IS '部门中的排序';
COMMENT ON COLUMN us_department_user.version IS '当前版本';
COMMENT ON COLUMN us_department_user.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN us_department_user.gmt_create IS '创建时间';
COMMENT ON COLUMN us_department_user.gmt_modified IS '修改时间';

-- 3. 部门表 (us_department)
CREATE TABLE us_department (
    id BIGINT PRIMARY KEY,
    tenant_id VARCHAR(255),
    source_dept_id VARCHAR(255),
    source_dept_parent_id VARCHAR(255),
    target_dept_id VARCHAR(255),
    target_dept_parent_id VARCHAR(255),
    name VARCHAR(255),
    name_en VARCHAR(255),
    "order" INTEGER,
    data_status INTEGER,
    sync_status INTEGER,
    version INTEGER,
    deleted INTEGER,
    gmt_create TIMESTAMP,
    gmt_modified TIMESTAMP
);

COMMENT ON TABLE us_department IS '部门表';
COMMENT ON COLUMN us_department.id IS '主键';
COMMENT ON COLUMN us_department.tenant_id IS '租户id';
COMMENT ON COLUMN us_department.source_dept_id IS '来源系统部门id';
COMMENT ON COLUMN us_department.source_dept_parent_id IS '来源系统部门上级id';
COMMENT ON COLUMN us_department.target_dept_id IS '企微部门id';
COMMENT ON COLUMN us_department.target_dept_parent_id IS '目标系统部门上级id';
COMMENT ON COLUMN us_department.name IS '部门名称';
COMMENT ON COLUMN us_department.name_en IS '部门英文名称';
COMMENT ON COLUMN us_department."order" IS '排序';
COMMENT ON COLUMN us_department.data_status IS '数据状态  1新增、2修改 、3删除';
COMMENT ON COLUMN us_department.sync_status IS '同步状态  0初始值 1同步中 2同步成功 3同步失败';
COMMENT ON COLUMN us_department.version IS '当前版本';
COMMENT ON COLUMN us_department.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN us_department.gmt_create IS '创建时间';
COMMENT ON COLUMN us_department.gmt_modified IS '修改时间';

-- 4. 成员信息同步配置表 (us_user_sync_config)
CREATE TABLE us_user_sync_config (
    id BIGINT PRIMARY KEY,
    tenant_id VARCHAR(255),
    sync_type INTEGER,
    sync_column VARCHAR(255),
    is_must INTEGER,
    is_ignore INTEGER,
    version INTEGER,
    deleted INTEGER,
    gmt_create TIMESTAMP,
    gmt_modified TIMESTAMP
);

COMMENT ON TABLE us_user_sync_config IS '成员信息同步配置';
COMMENT ON COLUMN us_user_sync_config.id IS '主键';
COMMENT ON COLUMN us_user_sync_config.tenant_id IS '租户id';
COMMENT ON COLUMN us_user_sync_config.sync_type IS '同步类型 1新增 2修改';
COMMENT ON COLUMN us_user_sync_config.sync_column IS '同步字段';
COMMENT ON COLUMN us_user_sync_config.is_must IS '是否必须  1是 0否';
COMMENT ON COLUMN us_user_sync_config.is_ignore IS '是否忽略 1是 0否';
COMMENT ON COLUMN us_user_sync_config.version IS '当前版本';
COMMENT ON COLUMN us_user_sync_config.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN us_user_sync_config.gmt_create IS '创建时间';
COMMENT ON COLUMN us_user_sync_config.gmt_modified IS '修改时间';

-- 5. 同步日志表 (us_sync_log)
CREATE TABLE us_sync_log (
    id BIGINT PRIMARY KEY,
    tenant_id VARCHAR(255),
    url VARCHAR(255),
    request TEXT,
    result TEXT,
    status VARCHAR(255),
    batch_no VARCHAR(255),
    version INTEGER,
    deleted INTEGER,
    gmt_create TIMESTAMP,
    gmt_modified TIMESTAMP
);

COMMENT ON TABLE us_sync_log IS '同步日志';
COMMENT ON COLUMN us_sync_log.id IS '主键';
COMMENT ON COLUMN us_sync_log.tenant_id IS '租户id';
COMMENT ON COLUMN us_sync_log.url IS '接口地址';
COMMENT ON COLUMN us_sync_log.request IS '参数';
COMMENT ON COLUMN us_sync_log.result IS '返回结果';
COMMENT ON COLUMN us_sync_log.status IS '接口状态';
COMMENT ON COLUMN us_sync_log.batch_no IS '批次号';
COMMENT ON COLUMN us_sync_log.version IS '当前版本';
COMMENT ON COLUMN us_sync_log.deleted IS '删除标记  0未删除 1已删除';
COMMENT ON COLUMN us_sync_log.gmt_create IS '创建时间';
COMMENT ON COLUMN us_sync_log.gmt_modified IS '修改时间';
