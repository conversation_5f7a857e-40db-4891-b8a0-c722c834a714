package com.wshoto.user.sync.app.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 定时任务配置
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("us_job")
public class Job implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 任务标识
     */
    @TableField("job_id")
    private String jobId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 定时cron配置
     */
    @TableField("cron")
    private String cron;

    /**
     * 任务备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 当前版本
     */
    @TableField("version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private Date gmtModified;


}
