package com.wshoto.user.sync.app.common.enums;
/**
 * 数据操作类型
 * <AUTHOR>
 */
public enum DataStatusEnum {
    /**
     * 新增
     */
    CREATE(1, "新增"),
    /**
     * 修改
     */
    UPDATE(2, "修改"),
    /**
     * 删除
     */
    DELETE(3, "删除"),
    ;

    private Integer code;
    private String desc;

    DataStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}