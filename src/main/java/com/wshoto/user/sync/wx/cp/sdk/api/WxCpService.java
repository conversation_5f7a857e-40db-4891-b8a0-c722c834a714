package com.wshoto.user.sync.wx.cp.sdk.api;

import com.wshoto.user.sync.wx.cp.sdk.common.error.WxErrorException;
import com.wshoto.user.sync.wx.cp.sdk.common.service.WxService;
import com.wshoto.user.sync.wx.cp.sdk.common.session.WxSession;
import com.wshoto.user.sync.wx.cp.sdk.common.session.WxSessionManager;
import com.wshoto.user.sync.wx.cp.sdk.common.util.http.MediaUploadRequestExecutor;
import com.wshoto.user.sync.wx.cp.sdk.common.util.http.RequestExecutor;
import com.wshoto.user.sync.wx.cp.sdk.common.util.http.RequestHttp;
import com.wshoto.user.sync.wx.cp.sdk.config.WxCpConfigStorage;

/**
 * 微信API的Service.
 *
 * <AUTHOR>
 */
public interface WxCpService extends WxService {

  /**
   * 获取access_token, 不强制刷新access_token
   *
   * @return the access token
   * @throws WxErrorException the wx error exception
   * @see #getAccessToken(boolean) #getAccessToken(boolean)#getAccessToken(boolean)#getAccessToken(boolean)
   */
  String getAccessToken() throws WxErrorException;

  /**
   * <pre>
   * 获取access_token，本方法线程安全
   * 且在多线程同时刷新时只刷新一次，避免超出2000次/日的调用次数上限
   * 另：本service的所有方法都会在access_token过期是调用此方法
   * 程序员在非必要情况下尽量不要主动调用此方法
   * 详情请见: http://mp.weixin.qq.com/wiki/index.php?title=获取access_token
   * </pre>
   *
   * @param forceRefresh 强制刷新
   * @return the access token
   * @throws WxErrorException the wx error exception
   */
  String getAccessToken(boolean forceRefresh) throws WxErrorException;

  /**
   * <pre>
   * 获取微信服务器的ip段
   * http://qydev.weixin.qq.com/wiki/index.php?title=回调模式#.E8.8E.B7.E5.8F.96.E5.BE.AE.E4.BF.A1.E6.9C.8D.E5.8A.A1.E5.99.A8.E7.9A.84ip.E6.AE.B5
   * </pre>
   *
   * @return { "ip_list": ["101.226.103.*", "101.226.62.*"] }
   * @throws WxErrorException the wx error exception
   */
  String[] getCallbackIp() throws WxErrorException;

  /**
   * 当不需要自动带accessToken的时候，可以用这个发起post请求
   *
   * @param url      接口地址
   * @param postData 请求body字符串
   * @return the string
   * @throws WxErrorException the wx error exception
   */
  String postWithoutToken(String url, String postData) throws WxErrorException;

  /**
   * <pre>
   * Service没有实现某个API的时候，可以用这个，
   * 比{@link #get}和{@link #post}方法更灵活，可以自己构造RequestExecutor用来处理不同的参数和不同的返回类型。
   * 可以参考，{@link MediaUploadRequestExecutor}的实现方法
   * </pre>
   *
   * @param <T>      请求值类型
   * @param <E>      返回值类型
   * @param executor 执行器
   * @param uri      请求地址
   * @param data     参数
   * @return the t
   * @throws WxErrorException the wx error exception
   */
  <T, E> T execute(RequestExecutor<T, E> executor, String uri, E data) throws WxErrorException;

  /**
   * <pre>
   * 设置当微信系统响应系统繁忙时，要等待多少 retrySleepMillis(ms) * 2^(重试次数 - 1) 再发起重试
   * 默认：1000ms
   * </pre>
   *
   * @param retrySleepMillis 重试休息时间
   */
  void setRetrySleepMillis(int retrySleepMillis);

  /**
   * <pre>
   * 设置当微信系统响应系统繁忙时，最大重试次数
   * 默认：5次
   * </pre>
   *
   * @param maxRetryTimes 最大重试次数
   */
  void setMaxRetryTimes(int maxRetryTimes);

  /**
   * 获取某个sessionId对应的session,如果sessionId没有对应的session，则新建一个并返回。
   *
   * @param id id可以为任意字符串，建议使用FromUserName作为id
   * @return the session
   */
  WxSession getSession(String id);

  /**
   * 获取某个sessionId对应的session,如果sessionId没有对应的session，若create为true则新建一个，否则返回null。
   *
   * @param id     id可以为任意字符串，建议使用FromUserName作为id
   * @param create 是否新建
   * @return the session
   */
  WxSession getSession(String id, boolean create);

  /**
   * 获取WxSessionManager 对象
   *
   * @return WxSessionManager session manager
   */
  WxSessionManager getSessionManager();

  /**
   * <pre>
   * 设置WxSessionManager，只有当需要使用个性化的WxSessionManager的时候才需要调用此方法，
   * WxCpService默认使用的是{@link com.wshoto.user.sync.wx.cp.sdk.common.session.StandardSessionManager}
   * </pre>
   *
   * @param sessionManager 会话管理器
   */
  void setSessionManager(WxSessionManager sessionManager);

  /**
   * 上传部门列表覆盖企业号上的部门信息
   *
   * @param mediaId 媒体id
   * @return the string
   * @throws WxErrorException the wx error exception
   */
  String replaceParty(String mediaId) throws WxErrorException;

  /**
   * 上传用户列表，增量更新成员
   *
   * @param mediaId 媒体id
   * @return jobId 异步任务id
   * @throws WxErrorException the wx error exception
   */
  String syncUser(String mediaId) throws WxErrorException;

  /**
   * 上传用户列表覆盖企业号上的用户信息
   *
   * @param mediaId 媒体id
   * @return the string
   * @throws WxErrorException the wx error exception
   */
  String replaceUser(String mediaId) throws WxErrorException;

  /**
   * 获取异步任务结果
   *
   * @param jobId 异步任务id
   * @return the task result
   * @throws WxErrorException the wx error exception
   */
  String getTaskResult(String jobId) throws WxErrorException;

  /**
   * 初始化http请求对象
   */
  void initHttp();

  /**
   * 获取WxCpConfigStorage 对象
   *
   * @return WxCpConfigStorage wx cp config storage
   */
  WxCpConfigStorage getWxCpConfigStorage();

  /**
   * 注入 {@link WxCpConfigStorage} 的实现
   *
   * @param wxConfigProvider 配置对象
   */
  void setWxCpConfigStorage(WxCpConfigStorage wxConfigProvider);

  /**
   * 获取部门相关接口的服务类对象
   *
   * @return the department service
   */
  com.wshoto.user.sync.wx.cp.sdk.api.WxCpDepartmentService getDepartmentService();

  /**
   * 获取媒体相关接口的服务类对象
   *
   * @return the media service
   */
  WxCpMediaService getMediaService();

  /**
   * 获取Oauth2相关接口的服务类对象
   *
   * @return the oauth 2 service
   */
  com.wshoto.user.sync.wx.cp.sdk.api.WxCpOAuth2Service getOauth2Service();

  /**
   * 获取标签相关接口的服务类对象
   *
   * @return the tag service
   */
  WxCpTagService getTagService();

  /**
   * 获取用户相关接口的服务类对象
   *
   * @return the user service
   */
  com.wshoto.user.sync.wx.cp.sdk.api.WxCpUserService getUserService();

  /**
   * http请求对象
   *
   * @return the request http
   */
  RequestHttp<?, ?> getRequestHttp();

  /**
   * Sets user service.
   *
   * @param userService the user service
   */
  void setUserService(WxCpUserService userService);

  /**
   * Sets department service.
   *
   * @param departmentService the department service
   */
  void setDepartmentService(WxCpDepartmentService departmentService);

  /**
   * Sets media service.
   *
   * @param mediaService the media service
   */
  void setMediaService(WxCpMediaService mediaService);

  /**
   * Sets oauth 2 service.
   *
   * @param oauth2Service the oauth 2 service
   */
  void setOauth2Service(WxCpOAuth2Service oauth2Service);

  /**
   * Sets tag service.
   *
   * @param tagService the tag service
   */
  void setTagService(WxCpTagService tagService);
}
