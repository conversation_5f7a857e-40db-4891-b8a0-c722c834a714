package com.wshoto.user.sync.wx.cp.sdk.bean;

import com.wshoto.user.sync.wx.cp.sdk.util.json.WxCpGsonHolder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by <PERSON>.
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxCpTag implements Serializable {
  private static final long serialVersionUID = -7243320279646928402L;

  private String id;

  private String name;


  /**
   * From json wx cp tag.
   *
   * @param json the json
   * @return the wx cp tag
   */
  public static WxCpTag fromJson(String json) {
    return WxCpGsonHolder.get().fromJson(json, WxCpTag.class);
  }

  /**
   * To json string.
   *
   * @return the string
   */
  public String toJson() {
    return WxCpGsonHolder.get().toJson(this);
  }

}
