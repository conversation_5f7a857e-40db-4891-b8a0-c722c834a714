package com.wshoto.user.sync.app.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.gson.Gson;
import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.common.enums.DataStatusEnum;
import com.wshoto.user.sync.app.common.enums.SyncStatusEnum;
import com.wshoto.user.sync.app.component.QwSyncComponent;
import com.wshoto.user.sync.app.dao.entity.DepartmentUser;
import com.wshoto.user.sync.app.dao.entity.Job;
import com.wshoto.user.sync.app.dao.entity.NotifyConfig;
import com.wshoto.user.sync.app.dao.entity.SyncLog;
import com.wshoto.user.sync.app.dao.entity.User;
import com.wshoto.user.sync.app.dao.entity.UserLoggedDTO;
import com.wshoto.user.sync.app.job.QwUserSyncJob;
import com.wshoto.user.sync.app.service.DepartmentUserService;
import com.wshoto.user.sync.app.service.JobService;
import com.wshoto.user.sync.app.service.NotifyConfigService;
import com.wshoto.user.sync.app.service.SyncLogService;
import com.wshoto.user.sync.app.service.UserLoggedService;
import com.wshoto.user.sync.app.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 通讯录同步
 */
@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("/admin/user-sync")
public class UserSyncController {

    private QwUserSyncJob qwUserSyncJob;

    private SyncLogService syncLogService;

    private JobService jobService;

    private NotifyConfigService notifyConfigService;

    private UserLoggedService userLoggedService;

    private UserService userService;

    private DepartmentUserService departmentUserService;

    private QwSyncComponent qwSyncComponent;

    /**
     * 通讯录同步
     *
     * @param corpId 企业ID
     * @param aadIds 指定需要同步的AAD ID列表，为空时同步所有用户
     * @return 同步批次号
     */
    @PostMapping("/{corpId}")
    public String createPoster(@PathVariable String corpId, @RequestBody(required = false) List<String> aadIds) {
        if (aadIds == null || aadIds.isEmpty()) {
            log.info("通讯录同步参数: {}", corpId);
        } else {
            log.info("通讯录同步参数: {}, 指定同步 {} 个用户", corpId, aadIds.size());
        }
        
        String batchNo = qwUserSyncJob.executeTask(corpId, aadIds);
        qwUserSyncJob.statisticSyncResult(corpId, batchNo);
        return batchNo;
    }

    @GetMapping("/batchNo/{batchNo}/logs")
    public Object querySyncLogByBatchNo(@PathVariable("batchNo") String batchNo) {
        List<SyncLog> syncLogList = syncLogService.list(new LambdaQueryWrapper<SyncLog>()
                .eq(SyncLog::getDeleted, JobConstant.DELETED_NO)
                .eq(SyncLog::getBatchNo, batchNo)
                .select(SyncLog::getUrl, SyncLog::getRequest, SyncLog::getResult, SyncLog::getStatus));
        return new Gson().toJson(syncLogList);
    }

    @PutMapping("/jobs/jobIds/{jobId}/manualUpdate")
    public List<Job> updateSyncJobCron(@PathVariable("jobId") String jobId, @RequestParam("cron") String cron) {
        jobService.update(new LambdaUpdateWrapper<Job>()
                .set(Job::getCron, cron)
                .eq(Job::getJobId, jobId)
                .eq(Job::getDeleted, JobConstant.DELETED_NO));

        return jobService.list(new LambdaQueryWrapper<Job>()
                .eq(Job::getJobId, jobId)
                .eq(Job::getDeleted, JobConstant.DELETED_NO));

    }

    @PutMapping("/notifyConfigs")
    public List<NotifyConfig> updateNotifyConfigs(@RequestParam("toUser") String toUser) {
        final int NOTIFY_TYPE_EMAIL = 2;
        notifyConfigService.update(new LambdaUpdateWrapper<NotifyConfig>()
                .set(NotifyConfig::getToUser, toUser)
                .set(NotifyConfig::getGmtModified, new Date())
                .eq(NotifyConfig::getNotifyType, NOTIFY_TYPE_EMAIL)
                .eq(NotifyConfig::getDeleted, JobConstant.DELETED_NO));

        log.info("update_notify_configs_admin new_to_user: {}", toUser);
        return notifyConfigService.list(new LambdaQueryWrapper<NotifyConfig>()
                .eq(NotifyConfig::getNotifyType, NOTIFY_TYPE_EMAIL)
                .eq(NotifyConfig::getDeleted, JobConstant.DELETED_NO));
    }

    /**
     * tick4.5发版时使用，业务人员只能给出1200+已登录员工邮箱，本接口用于补充邮箱和aad_id
     */
    @PostMapping("/loggedUsers/syncAadId")
    public List<UserLoggedDTO> updateLoggedUsersAad(@RequestParam("sourceUserIds") Set<String> sourceUserIds) {
        List<User> loggedUsers = userService
                .list(new LambdaQueryWrapper<User>().in(User::getSourceUserid, sourceUserIds));

        List<UserLoggedDTO> userLoggedDTOList = new ArrayList<>();
        loggedUsers.forEach(loggedUser -> {
            if (StringUtils.isEmpty(loggedUser.getAadId())) {
                log.error("userSync/loggedUsers/syncAadId: aadId is empty, sourceUserId: {}",
                        JSONUtil.toJsonStr(loggedUser));
                return;
            }

            UserLoggedDTO userLoggedDTO = new UserLoggedDTO();
            userLoggedDTO.setAadId(loggedUser.getAadId());
            userLoggedDTO.setSourceUserid(loggedUser.getSourceUserid());
            userLoggedDTOList.add(userLoggedDTO);
        });
        userLoggedService.saveBatch(userLoggedDTOList);
        return userLoggedService.list();
    }

    /**
     * tick4.5发版时使用，将1200+已登录员工之外的员工在us_user表中全部置为删除状态
     */
    @PutMapping("/notLoggedUsers/updateState")
    public List<User> updateUsUserState() {
        List<UserLoggedDTO> userLoggedDTOList = userLoggedService.list();
        List<String> userLoggedAadIds = userLoggedDTOList.stream().map(UserLoggedDTO::getAadId).distinct().toList();

        userService.update(new LambdaUpdateWrapper<User>()
                .notIn(User::getAadId, userLoggedAadIds)
                .eq(User::getDeleted, JobConstant.DELETED_NO)
                .set(User::getSyncStatus, SyncStatusEnum.INIT.getCode())
                .set(User::getDataStatus, DataStatusEnum.DELETE.getCode())
                .set(User::getGmtModified, new Date()));
        return userService.list();
    }

    /**
     * tick4.5发版时使用，将1200+已登录员工之外的员工在企微提交删除请求
     */
    @PutMapping("/notLoggedUsers/deleteInWechat")
    public void deleteNotLoggedUsersInWechat() {
        List<UserLoggedDTO> userLoggedDTOList = userLoggedService.list();
        List<String> userLoggedAadIds = userLoggedDTOList.stream().map(UserLoggedDTO::getAadId).distinct().toList();

        List<User> userDeleteList = userService.list(new LambdaQueryWrapper<User>()
                .notIn(User::getAadId, userLoggedAadIds)
                .eq(User::getDeleted, JobConstant.DELETED_NO)
                .eq(User::getSyncStatus, SyncStatusEnum.INIT.getCode())
                .eq(User::getDataStatus, DataStatusEnum.DELETE.getCode()));

        for (User user : userDeleteList) {
            qwSyncComponent.deleteUserOldManner(user, "manuel-delete-not-logged-users-in-wechat");
        }
    }

    /**
     * tick4.5发版时使用，1200+已登录员工之外的员工【已在企微删除，为避免脏数据】，在us_user表和us_department_user表中删除旧数据
     */
    @DeleteMapping("/notLoggedUsers")
    public List<User> deleteObjectUsUser() {
        List<UserLoggedDTO> userLoggedDTOList = userLoggedService.list();
        List<String> userLoggedAadIds = userLoggedDTOList.stream().map(UserLoggedDTO::getAadId).distinct().toList();

        userService.remove(new LambdaQueryWrapper<User>()
                .notIn(User::getAadId, userLoggedAadIds));

        departmentUserService.remove(new LambdaQueryWrapper<DepartmentUser>()
                .notIn(DepartmentUser::getAadId, userLoggedAadIds));
        return userService.list();
    }

    /**
     * 同步指定AAD ID的用户到企业微信
     *
     * @param tenantId 租户ID
     * @param aadIds   AAD ID列表
     * @return 同步结果报告
     */
    @PostMapping("/users/sync")
    public QwUserSyncJob.SyncReport syncSpecificUsers(
            @RequestParam(value = "tenantId", defaultValue = "ww6ffc4f642bca6ae8") String tenantId,
            @RequestBody List<String> aadIds) {

        log.info("开始同步指定用户 - 租户: {}, AAD ID数量: {}", tenantId, aadIds != null ? aadIds.size() : 0);

        var result = qwUserSyncJob.syncUsersByAadIds(tenantId, aadIds);

        log.info("指定用户同步完成 - 批次: {}, 成功: {}, 失败: {}",
                result.getBatchNo(), result.getSuccessCount(), result.getFailedCount());
        var syncLogList = syncLogService.list(new LambdaQueryWrapper<SyncLog>()
                .eq(SyncLog::getBatchNo, result.getBatchNo())
                .select(SyncLog::getUrl, SyncLog::getRequest, SyncLog::getResult, SyncLog::getStatus));
        result.setReport(syncLogList);
        return result;
    }
}
