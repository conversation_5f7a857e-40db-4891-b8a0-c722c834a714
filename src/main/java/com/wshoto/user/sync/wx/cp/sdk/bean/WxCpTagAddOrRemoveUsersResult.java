package com.wshoto.user.sync.wx.cp.sdk.bean;

import com.google.common.base.Splitter;
import com.google.gson.annotations.SerializedName;
import com.wshoto.user.sync.wx.cp.sdk.util.json.WxCpGsonHolder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 为标签添加或移除用户结果对象类.
 * 
 *
 * <AUTHOR>
 */
@Data
public class WxCpTagAddOrRemoveUsersResult implements Serializable {
  private static final long serialVersionUID = 1420065684270213578L;

  @Override
  public String toString() {
    return WxCpGsonHolder.get().toJson(this);
  }

  /**
   * From json wx cp tag add or remove users result.
   *
   * @param json the json
   * @return the wx cp tag add or remove users result
   */
  public static WxCpTagAddOrRemoveUsersResult fromJson(String json) {
    return WxCpGsonHolder.get().fromJson(json, WxCpTagAddOrRemoveUsersResult.class);
  }

  @SerializedName("errcode")
  private Integer errCode;

  @SerializedName("errmsg")
  private String errMsg;

  @SerializedName("invalidlist")
  private String invalidUsers;

  @SerializedName("invalidparty")
  private String[] invalidParty;

  /**
   * Gets invalid user list.
   *
   * @return the invalid user list
   */
  public List<String> getInvalidUserList() {
    return this.content2List(this.invalidUsers);
  }

  private List<String> content2List(String content) {
    if (StringUtils.isBlank(content)) {
      return Collections.emptyList();
    }

    return Splitter.on("|").splitToList(content);
  }

}
