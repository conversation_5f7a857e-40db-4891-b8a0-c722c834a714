package com.wshoto.user.sync.app.job;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.dao.entity.Job;
import com.wshoto.user.sync.app.service.JobService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@SuppressWarnings("unchecked")
@ExtendWith({MockitoExtension.class})
class CommonScheduledTaskConfigTest {

    @Mock
    private JobService jobService;

    @Mock
    private QwUserSyncJob qwUserSyncJob;

    @InjectMocks
    private CommonScheduledTaskConfig commonScheduledTaskConfig;

    @Test
    @DisplayName("configureTasks_空任务列表_不执行任务")
    void configureTasks_emptyJobList_noTasksConfigured() {
        // Arrange
        ScheduledTaskRegistrar taskRegistrar = new ScheduledTaskRegistrar();
        when(jobService.list(any(Wrapper.class))).thenReturn(List.of());

        // Act
        commonScheduledTaskConfig.configureTasks(taskRegistrar);

        // Assert
        verify(jobService, times(1)).list(any(Wrapper.class));
        verifyNoInteractions(qwUserSyncJob);
    }

    @Test
    @DisplayName("configureTasks_任务cron为空_任务取消")
    void configureTasks_jobWithoutCron_taskCancelled() {
        // Arrange
        ScheduledTaskRegistrar taskRegistrar = new ScheduledTaskRegistrar();
        Job invalidJob = new Job();
        invalidJob.setCron(null);  // Missing cron
        invalidJob.setJobId("test-job");
        invalidJob.setTenantId("test-tenant");
        when(jobService.list(any(Wrapper.class))).thenReturn(List.of(invalidJob));

        // Act
        commonScheduledTaskConfig.configureTasks(taskRegistrar);

        // Assert
        verify(jobService, times(1)).list(any(Wrapper.class));
        verifyNoInteractions(qwUserSyncJob);
    }

    @Test
    @DisplayName("configureTasks_任务jobId为空_任务取消")
    void configureTasks_jobWithoutJobId_taskCancelled() {
        // Arrange
        ScheduledTaskRegistrar taskRegistrar = new ScheduledTaskRegistrar();
        Job invalidJob = new Job();
        invalidJob.setCron("0/5 * * * * ?");
        invalidJob.setJobId(null);  // Missing job ID
        invalidJob.setTenantId("test-tenant");
        when(jobService.list(any(Wrapper.class))).thenReturn(List.of(invalidJob));

        // Act
        commonScheduledTaskConfig.configureTasks(taskRegistrar);

        // Assert
        verify(jobService, times(1)).list(any(Wrapper.class));
        verifyNoInteractions(qwUserSyncJob);
    }

    @Test
    @DisplayName("configureTasks_有效任务_配置定时任务成功")
    void configureTasks_validJob_taskConfiguredSuccessfully() {
        // Arrange
        Job validJob = new Job();
        validJob.setCron("0/5 * * * * ?");
        validJob.setJobId(JobConstant.QW_USER_SYNC_JOB_ID);
        validJob.setTenantId("test-tenant");
        when(jobService.list(any(Wrapper.class))).thenReturn(List.of(validJob));
        ScheduledTaskRegistrar scheduledTaskRegistrar = new ScheduledTaskRegistrar();

        // Act
        assertThatNoException().isThrownBy(() -> commonScheduledTaskConfig.configureTasks(scheduledTaskRegistrar));

        // Assert
        verify(jobService, times(1)).list(any(Wrapper.class));
    }

}