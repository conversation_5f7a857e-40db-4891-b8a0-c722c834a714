package com.wshoto.user.sync.wx.cp.sdk.api.impl;

import com.wshoto.user.sync.wx.cp.sdk.common.bean.WxAccessToken;
import com.wshoto.user.sync.wx.cp.sdk.common.enums.WxType;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxError;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxErrorException;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxRuntimeException;
import com.wshoto.user.sync.wx.cp.sdk.config.WxCpConfigStorage;
import com.wshoto.user.sync.wx.cp.sdk.constant.WxCpApiPathConsts;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;

import java.io.IOException;
import java.util.concurrent.locks.Lock;

/**
 * <pre>
 *  默认接口实现类，使用apache httpclient实现
 * 
 * </pre>
 * <pre>
 * 增加分布式锁（基于WxCpConfigStorage实现）的支持
 * Updated by yuanqixun on 2020-05-13
 * </pre>
 *
 * <AUTHOR>
 */
public class WxCpServiceImpl extends WxCpServiceApacheHttpClientImpl {
  @Override
  public String getAccessToken(boolean forceRefresh) throws WxErrorException {
    final WxCpConfigStorage configStorage = getWxCpConfigStorage();
    if (!configStorage.isAccessTokenExpired() && !forceRefresh) {
      return configStorage.getAccessToken();
    }
    Lock lock = configStorage.getAccessTokenLock();
    lock.lock();
    try {
      // 拿到锁之后，再次判断一下最新的token是否过期，避免重刷
      if (!configStorage.isAccessTokenExpired() && !forceRefresh) {
        return configStorage.getAccessToken();
      }
      String url = String.format(configStorage.getApiUrl(WxCpApiPathConsts.GET_TOKEN),
        this.configStorage.getCorpId(), this.configStorage.getCorpSecret());
      try {
        HttpGet httpGet = new HttpGet(url);
        if (getRequestHttpProxy() != null) {
          RequestConfig config = RequestConfig.custom().setProxy(getRequestHttpProxy()).build();
          httpGet.setConfig(config);
        }
        String resultContent;
        try (CloseableHttpClient httpClient = getRequestHttpClient();
             CloseableHttpResponse response = httpClient.execute(httpGet)) {
          resultContent = new BasicResponseHandler().handleResponse(response);
        } finally {
          httpGet.releaseConnection();
        }
        WxError error = WxError.fromJson(resultContent, WxType.CP);
        if (error.getErrorCode() != 0) {
          throw new WxErrorException(error);
        }

        WxAccessToken accessToken = WxAccessToken.fromJson(resultContent);
        configStorage.updateAccessToken(accessToken.getAccessToken(), accessToken.getExpiresIn());
      } catch (IOException e) {
        throw new WxRuntimeException(e);
      }
    } finally {
      lock.unlock();
    }
    return configStorage.getAccessToken();
  }
}
