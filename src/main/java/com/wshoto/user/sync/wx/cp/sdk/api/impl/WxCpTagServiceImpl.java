package com.wshoto.user.sync.wx.cp.sdk.api.impl;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.google.gson.reflect.TypeToken;
import com.wshoto.user.sync.wx.cp.sdk.api.WxCpService;
import com.wshoto.user.sync.wx.cp.sdk.api.WxCpTagService;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpTag;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpTagAddOrRemoveUsersResult;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpTagGetResult;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpUser;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxErrorException;
import com.wshoto.user.sync.wx.cp.sdk.common.util.json.GsonParser;
import com.wshoto.user.sync.wx.cp.sdk.util.json.WxCpGsonHolder;
import lombok.RequiredArgsConstructor;

import java.util.List;

import static com.wshoto.user.sync.wx.cp.sdk.constant.WxCpApiPathConsts.Tag.*;

/**
 * <pre>
 *  标签管理接口.
 * 
 * </pre>
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class WxCpTagServiceImpl implements WxCpTagService {
  private final WxCpService mainService;

  @Override
  public String create(String name, Integer id) throws WxErrorException {
    JsonObject o = new JsonObject();
    o.addProperty("tagname", name);

    if (id != null) {
      o.addProperty("tagid", id);
    }
    return this.create(o);
  }

  private String create(JsonObject param) throws WxErrorException {
    String url = this.mainService.getWxCpConfigStorage().getApiUrl(TAG_CREATE);
    String responseContent = this.mainService.post(url, param.toString());
    JsonObject jsonObject = GsonParser.parse(responseContent);
    return jsonObject.get("tagid").getAsString();
  }

  @Override
  public void update(String tagId, String tagName) throws WxErrorException {
    String url = this.mainService.getWxCpConfigStorage().getApiUrl(TAG_UPDATE);
    JsonObject o = new JsonObject();
    o.addProperty("tagid", tagId);
    o.addProperty("tagname", tagName);
    this.mainService.post(url, o.toString());
  }

  @Override
  public void delete(String tagId) throws WxErrorException {
    String url = String.format(this.mainService.getWxCpConfigStorage().getApiUrl(TAG_DELETE), tagId);
    this.mainService.get(url, null);
  }

  @Override
  public List<WxCpTag> listAll() throws WxErrorException {
    String url = this.mainService.getWxCpConfigStorage().getApiUrl(TAG_LIST);
    String responseContent = this.mainService.get(url, null);
    JsonObject tmpJson = GsonParser.parse(responseContent);
    return WxCpGsonHolder.get()
      .fromJson(
        tmpJson.get("taglist"),
        new TypeToken<List<WxCpTag>>() {
        }.getType()
      );
  }

  @Override
  public List<WxCpUser> listUsersByTagId(String tagId) throws WxErrorException {
    String url = String.format(this.mainService.getWxCpConfigStorage().getApiUrl(TAG_GET), tagId);
    String responseContent = this.mainService.get(url, null);
    JsonObject tmpJson = GsonParser.parse(responseContent);
    return WxCpGsonHolder.get()
      .fromJson(
        tmpJson.get("userlist"),
        new TypeToken<List<WxCpUser>>() {
        }.getType()
      );
  }

  @Override
  public WxCpTagAddOrRemoveUsersResult addUsers2Tag(String tagId, List<String> userIds, List<String> partyIds) throws WxErrorException {
    String url = this.mainService.getWxCpConfigStorage().getApiUrl(TAG_ADD_TAG_USERS);
    JsonObject jsonObject = new JsonObject();
    jsonObject.addProperty("tagid", tagId);
    this.addUserIdsAndPartyIdsToJson(userIds, partyIds, jsonObject);

    return WxCpTagAddOrRemoveUsersResult.fromJson(this.mainService.post(url, jsonObject.toString()));
  }

  @Override
  public WxCpTagAddOrRemoveUsersResult removeUsersFromTag(String tagId, List<String> userIds, List<String> partyIds) throws WxErrorException {
    String url = this.mainService.getWxCpConfigStorage().getApiUrl(TAG_DEL_TAG_USERS);
    JsonObject jsonObject = new JsonObject();
    jsonObject.addProperty("tagid", tagId);
    this.addUserIdsAndPartyIdsToJson(userIds, partyIds, jsonObject);

    return WxCpTagAddOrRemoveUsersResult.fromJson(this.mainService.post(url, jsonObject.toString()));
  }

  private void addUserIdsAndPartyIdsToJson(List<String> userIds, List<String> partyIds, JsonObject jsonObject) {
    if (userIds != null) {
      JsonArray jsonArray = new JsonArray();
      for (String userId : userIds) {
        jsonArray.add(new JsonPrimitive(userId));
      }
      jsonObject.add("userlist", jsonArray);
    }

    if (partyIds != null) {
      JsonArray jsonArray = new JsonArray();
      for (String userId : partyIds) {
        jsonArray.add(new JsonPrimitive(userId));
      }
      jsonObject.add("partylist", jsonArray);
    }
  }

  @Override
  public WxCpTagGetResult get(String tagId) throws WxErrorException {
    if (tagId == null) {
      throw new IllegalArgumentException("缺少tagId参数");
    }

    String url = String.format(this.mainService.getWxCpConfigStorage().getApiUrl(TAG_GET), tagId);
    String responseContent = this.mainService.get(url, null);
    return WxCpTagGetResult.fromJson(responseContent);
  }
}
