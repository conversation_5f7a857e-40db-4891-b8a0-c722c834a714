package com.wshoto.user.sync.wx.cp.sdk.api.impl;

import com.wshoto.user.sync.wx.cp.sdk.api.WxCpMediaService;
import com.wshoto.user.sync.wx.cp.sdk.api.WxCpService;
import com.wshoto.user.sync.wx.cp.sdk.common.bean.result.WxMediaUploadResult;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxErrorException;
import com.wshoto.user.sync.wx.cp.sdk.common.util.fs.FileUtils;
import com.wshoto.user.sync.wx.cp.sdk.common.util.http.BaseMediaDownloadRequestExecutor;
import com.wshoto.user.sync.wx.cp.sdk.common.util.http.InputStreamData;
import com.wshoto.user.sync.wx.cp.sdk.common.util.http.MediaInputStreamUploadRequestExecutor;
import com.wshoto.user.sync.wx.cp.sdk.common.util.http.MediaUploadRequestExecutor;
import lombok.RequiredArgsConstructor;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.UUID;

import static com.wshoto.user.sync.wx.cp.sdk.constant.WxCpApiPathConsts.Media.*;


/**
 * <pre>
 * 媒体管理接口.
 * 
 * </pre>
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class WxCpMediaServiceImpl implements WxCpMediaService {
  private final WxCpService mainService;

  @Override
  public WxMediaUploadResult upload(String mediaType, String fileType, InputStream inputStream)
    throws WxErrorException, IOException {
    return this.upload(mediaType, FileUtils.createTmpFile(inputStream, UUID.randomUUID().toString(), fileType));
  }

  @Override
  public WxMediaUploadResult upload(String mediaType, String filename, String url) throws WxErrorException,
    IOException {
    HttpURLConnection conn = null;
    InputStream inputStream = null;
    try {
      URL remote = new URL(url);
      conn = (HttpURLConnection) remote.openConnection();
      //设置超时间为3秒
      conn.setConnectTimeout(60 * 1000);
      //防止屏蔽程序抓取而返回403错误
      conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
      inputStream = conn.getInputStream();
      return this.mainService.execute(MediaInputStreamUploadRequestExecutor.create(this.mainService.getRequestHttp())
        , this.mainService.getWxCpConfigStorage().getApiUrl(MEDIA_UPLOAD + mediaType),
        new InputStreamData(inputStream, filename));
    } finally {
      if (inputStream != null) {
        try {
          inputStream.close();
        } catch (IOException e) {
        }
      }
      if (conn != null) {
        conn.disconnect();
      }
    }
  }

  @Override
  public WxMediaUploadResult upload(String mediaType, File file) throws WxErrorException {
    return this.mainService.execute(MediaUploadRequestExecutor.create(this.mainService.getRequestHttp()),
      this.mainService.getWxCpConfigStorage().getApiUrl(MEDIA_UPLOAD + mediaType), file);
  }

  @Override
  public File download(String mediaId) throws WxErrorException {
    return this.mainService.execute(
      BaseMediaDownloadRequestExecutor.create(this.mainService.getRequestHttp(),
        this.mainService.getWxCpConfigStorage().getTmpDirFile()),
      this.mainService.getWxCpConfigStorage().getApiUrl(MEDIA_GET), "media_id=" + mediaId);
  }

  @Override
  public File getJssdkFile(String mediaId) throws WxErrorException {
    return this.mainService.execute(
      BaseMediaDownloadRequestExecutor.create(this.mainService.getRequestHttp(),
        this.mainService.getWxCpConfigStorage().getTmpDirFile()),
      this.mainService.getWxCpConfigStorage().getApiUrl(JSSDK_MEDIA_GET), "media_id=" + mediaId);
  }

  @Override
  public String uploadImg(File file) throws WxErrorException {
    final String url = this.mainService.getWxCpConfigStorage().getApiUrl(IMG_UPLOAD);
    return this.mainService.execute(MediaUploadRequestExecutor.create(this.mainService.getRequestHttp()), url, file)
      .getUrl();
  }
}
