package com.wshoto.user.sync.app.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 成员信息同步配置
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("us_user_sync_config")
public class UserSyncConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 同步类型 1新增 2修改
     */
    @TableField("sync_type")
    private Integer syncType;

    /**
     * 同步字段
     */
    @TableField("sync_column")
    private String syncColumn;

    /**
     * 是否必须  1是 0否
     */
    @TableField("is_must")
    private Integer isMust;

    /**
     * 是否忽略 1是 0否
     */
    @TableField("is_ignore")
    private Integer isIgnore;

    /**
     * 当前版本
     */
    @TableField("version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private Date gmtModified;


}
