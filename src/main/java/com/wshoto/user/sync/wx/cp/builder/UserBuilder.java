package com.wshoto.user.sync.wx.cp.builder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.wshoto.user.sync.app.common.constant.UserColumnConstant;
import com.wshoto.user.sync.app.common.dto.Attr;
import com.wshoto.user.sync.app.common.dto.Extattr;
import com.wshoto.user.sync.app.common.dto.ExternalProfile;
import com.wshoto.user.sync.app.common.dto.WechatChannels;
import com.wshoto.user.sync.app.dao.entity.DepartmentUser;
import com.wshoto.user.sync.app.dao.entity.User;
import com.wshoto.user.sync.app.dao.entity.UserSyncConfig;
import com.wshoto.user.sync.wx.cp.sdk.bean.Gender;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpUser;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxRuntimeException;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *  <AUTHOR>
 */
public class UserBuilder {

  public static WxCpUser build(User user, List<DepartmentUser> departmentUserList
          , Map<String, UserSyncConfig> configMap, Map<String, Method> userField) {
    WxCpUser wxCpUser = new WxCpUser();
    if(checkConfig(UserColumnConstant.USERID,configMap,user,userField)){
      wxCpUser.setUserId(user.getAadId());
    }
    if(checkConfig(UserColumnConstant.NAME,configMap,user, userField)){
      wxCpUser.setName(user.getName());
    }
    if(checkConfig(UserColumnConstant.POSITION,configMap,user, userField)){
      wxCpUser.setPosition(user.getPosition());
    }
    if(checkConfig(UserColumnConstant.MOBILE,configMap,user, userField)){
      wxCpUser.setMobile(user.getMobile());
    }
    if(checkConfig(UserColumnConstant.GENDER,configMap,user, userField)){
      wxCpUser.setGender(Gender.fromCode(user.getGender()));
    }
    if(checkConfig(UserColumnConstant.EMAIL,configMap,user, userField)){
      wxCpUser.setEmail(user.getEmail());
    }
    if(checkConfig(UserColumnConstant.BIZ_MAIL,configMap,user, userField)){
      wxCpUser.setBizMail(user.getBizMail());
    }
    if(checkConfig(UserColumnConstant.MAIN_DEPARTMENT,configMap,user, userField)){
      if(user.getMainDepartment() != null){
        wxCpUser.setMainDepartment(String.valueOf(user.getMainDepartment()));
      }
    }
    if(checkConfig(UserColumnConstant.ADDRESS,configMap,user, userField)){
      wxCpUser.setAddress(user.getAddress());
    }
    if(checkConfig(UserColumnConstant.ENABLE,configMap,user, userField)){
      wxCpUser.setEnable(user.getEnable());
    }
    if(checkConfig(UserColumnConstant.ALIAS,configMap,user, userField)){
      wxCpUser.setAlias(user.getAlias());
    }
    if(checkConfig(UserColumnConstant.TELEPHONE,configMap,user, userField)){
      wxCpUser.setTelephone(user.getTelephone());
    }
    if(checkConfig(UserColumnConstant.TO_INVITE,configMap,user, userField)){
      wxCpUser.setToInvite(user.getToInvite()==1?true:false);
    }
    if(checkConfig(UserColumnConstant.EXTATTR,configMap,user, userField)){
      String extattrStr = user.getExtattr();
      if(StrUtil.isNotEmpty(extattrStr)){
        Extattr extattr = JSONUtil.toBean(extattrStr, Extattr.class);
        List<Attr> attrs = extattr.getAttrs();
        if(CollectionUtil.isNotEmpty(attrs)){
          for (Attr attr : attrs) {
            WxCpUser.Attr attr1 = new WxCpUser.Attr();
            attr1.setType(attr.getType());
            attr1.setName(attr.getName());
            if(attr.getText() != null){
              attr1.setTextValue(attr.getText()==null?"":attr.getText().getValue());
            }else if(attr.getWeb() != null){
              attr1.setWebUrl(attr.getWeb().getUrl());
              attr1.setWebTitle(attr.getWeb().getTitle());
            }
            wxCpUser.addExtAttr(attr1);
          }
        }
      }
    }
    if(checkConfig(UserColumnConstant.EXTERNAL_PROFILE,configMap,user, userField)){
      if(StrUtil.isNotEmpty(user.getExternalProfile())){
        ExternalProfile extattr = JSONUtil.toBean(user.getExternalProfile(), ExternalProfile.class);
        if(extattr != null){
          wxCpUser.setExternalCorpName(extattr.getExternalCorpName());
          WechatChannels wechatChannels = extattr.getWechatChannels();
          if(wechatChannels != null){
            WxCpUser.WechatChannels wechatChannels1  = new WxCpUser.WechatChannels();
            wechatChannels1.setNickname(wechatChannels.getNickname());
            wxCpUser.setWechatChannels(wechatChannels1);
          }
        }
        if(CollectionUtil.isNotEmpty(extattr.getExternalAttr())){
          List<WxCpUser.ExternalAttribute> externalAttrs = new ArrayList<>();
          for (Attr item : extattr.getExternalAttr()) {
            WxCpUser.ExternalAttribute externalAttribute = new WxCpUser.ExternalAttribute();
            externalAttribute.setType(item.getType());
            externalAttribute.setName(item.getName());
            if(item.getText() != null){
              externalAttribute.setValue(item.getText()==null?"":item.getText().getValue());
            }else if(item.getWeb() != null){
              externalAttribute.setUrl(item.getWeb().getUrl());
              externalAttribute.setTitle(item.getWeb().getTitle());
            } else if(item.getMiniprogram() != null){
              externalAttribute.setAppid(item.getMiniprogram().getAppid());
              externalAttribute.setPagePath(item.getMiniprogram().getPagepath());
              externalAttribute.setTitle(item.getMiniprogram().getTitle());
            }
            externalAttrs.add(externalAttribute);
          }
          wxCpUser.setExternalAttrs(externalAttrs);
        }
      }
    }
    if(checkConfig(UserColumnConstant.EXTERNAL_POSITION,configMap,user, userField)){
      wxCpUser.setExternalPosition(user.getExternalPosition());
    }
    if(checkConfig(UserColumnConstant.DIRECT_LEADER,configMap,user, userField)){
      if(StrUtil.isNotEmpty(user.getDirectLeader())){
        wxCpUser.setDirectLeader(Lists.newArrayList(user.getDirectLeader()).toArray(new String[1]));
      }
    }
    if(CollectionUtil.isNotEmpty(departmentUserList)){
      List<Long> deptIds = new ArrayList<>();
      List<Integer> orderList = new ArrayList<>();
      List<Integer> isLeaderList = new ArrayList<>();
      for (DepartmentUser item : departmentUserList) {
        deptIds.add(Long.valueOf(item.getTargetDeptId()));
        orderList.add(item.getDeptOrder()==null?0:item.getDeptOrder());
        isLeaderList.add(item.getIsDeptLeader()==null?0:item.getIsDeptLeader());
      }
      if(checkConfig(UserColumnConstant.DEPARTMENT,configMap,user, userField)) {
        wxCpUser.setDepartIds(deptIds.toArray(new Long[deptIds.size()]));
      }
      if(checkConfig(UserColumnConstant.ORDER,configMap,user, userField)) {
        wxCpUser.setOrders(orderList.toArray(new Integer[orderList.size()]));
      }
      if(checkConfig(UserColumnConstant.IS_LEADER_IN_DEPT,configMap,user, userField)) {
        wxCpUser.setIsLeaderInDept(isLeaderList.toArray(new Integer[isLeaderList.size()]));
      }
    }
    return wxCpUser;
  }

  private static boolean checkConfig(String column, Map<String, UserSyncConfig> configMap
          , User user, Map<String, Method> userFieldMethodMap) {
    UserSyncConfig userSyncConfig = configMap.get(column);
    //字段未配置，不校验
    if(userSyncConfig == null){
      return true;
    }
    //字段忽略
    if(userSyncConfig.getIsIgnore() == 1){
      return false;
    }
    //字段必须
    if(userSyncConfig.getIsMust() == 1){
      Method method = userFieldMethodMap.get(column);
      if(method != null){
        try {
          Object invoke = method.invoke(user);
          if(ObjectUtil.isEmpty(invoke)){
            throw new WxRuntimeException(StrUtil.format("字段{}不能为空",column));
          }
          return true;
        } catch (Exception e) {
          throw new WxRuntimeException(e);
        }
      }
    }
    return true;
  }

}
