package com.wshoto.user.sync.wx.cp.sdk.common.bean.ocr;

import com.google.gson.annotations.SerializedName;
import com.wshoto.user.sync.wx.cp.sdk.common.util.json.WxGsonBuilder;
import lombok.Data;

import java.io.Serializable;

/**
 * OCR身份证识别结果.
 *
 * <AUTHOR>
 * created on  2019-06-23
 */
@Data
public class WxOcrIdCardResult implements Serializable {
  private static final long serialVersionUID = 8184352486986729980L;

  @SerializedName("type")
  private String type;
  @SerializedName("name")
  private String name;
  @SerializedName("id")
  private String id;
  @SerializedName("addr")
  private String addr;
  @SerializedName("gender")
  private String gender;
  @SerializedName("nationality")
  private String nationality;
  @SerializedName("valid_date")
  private String validDate;

  public static WxOcrIdCardResult fromJson(String json) {
    return WxGsonBuilder.create().from<PERSON><PERSON>(json, WxOcrIdCardResult.class);
  }

}
