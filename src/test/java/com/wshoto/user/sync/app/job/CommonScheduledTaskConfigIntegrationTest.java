package com.wshoto.user.sync.app.job;

import com.wshoto.user.sync.app.BaseIntegrationTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.config.TriggerTask;

/**
 * CommonScheduledTaskConfig测试类
 *
 * <AUTHOR>
 * @date 2024-7-16
 */
class CommonScheduledTaskConfigIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private CommonScheduledTaskConfig commonScheduledTaskConfig;

    @Test
    void _01_configureTasks_success() {
        ScheduledTaskRegistrar scheduledTaskRegistrar = new ScheduledTaskRegistrar();

        // 执行待验证部分
        commonScheduledTaskConfig.configureTasks(scheduledTaskRegistrar);

        // 验证执行结果
        Assertions.assertEquals(1, scheduledTaskRegistrar.getTriggerTaskList()
                                                         .size());

        TriggerTask triggerTask = scheduledTaskRegistrar.getTriggerTaskList()
                                                        .get(0);
        Assertions.assertNotNull(triggerTask.getTrigger());
    }

}
