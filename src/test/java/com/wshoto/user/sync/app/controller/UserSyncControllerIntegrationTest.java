package com.wshoto.user.sync.app.controller;

import com.wshoto.user.sync.app.BaseIntegrationTest;
import com.wshoto.user.sync.app.dao.entity.NotifyConfig;
import com.wshoto.user.sync.app.dao.entity.User;
import com.wshoto.user.sync.app.dao.mapper.UserMapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.instancio.Instancio;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.notNullValue;
import static org.instancio.Select.field;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

@DisplayName("员工信息同步Controller测试类")
class UserSyncControllerIntegrationTest extends BaseIntegrationTest {

    @Resource
    UserMapper userMapper;

    @Test
    @DisplayName("updateSyncJobCron-输入有效数据-更新成功")
    void updateSyncJobCron_validInput_updatesCron() {
        // Arrange
        String jobId = "testJobId";
        String newCron = "0 0 12 * * ?";

        // Act & Assert
        given().pathParam("jobId", jobId)
               .queryParam("cron", newCron)
               .when()
               .put("/admin/user-sync/jobs/jobIds/{jobId}/manualUpdate")
               .then()
               .statusCode(200)
               .body(notNullValue());
    }

    @Test
    @DisplayName("updateNotifyConfigs-正常场景")
    void updateNotifyConfigs_success() {
        // Arrange
        String toUserNew = Math.random() + StringUtils.EMPTY;

        // Act
        List<NotifyConfig> notifyConfigListActual = given().queryParam("toUser", toUserNew)
                                                           .when()
                                                           .put("/admin/user-sync/notifyConfigs")
                                                           .then()
                                                           .statusCode(200)
                                                           .extract()
                                                           .jsonPath()
                                                           .getList(".", NotifyConfig.class);

        // Assert
        assertFalse(CollectionUtils.isEmpty(notifyConfigListActual));
        assertEquals(toUserNew, notifyConfigListActual.get(0)
                                                      .getToUser());
    }

    @Test
    @DisplayName("createPoster-有效corpId-返回batchNo")
    void createPoster_validInput_returnsBatchNo() {
        // Arrange
        String corpId = "1";

        // Act & Assert
        given().pathParam("corpId", corpId)
               .contentType("application/json")
               .when()
               .post("/admin/user-sync/{corpId}")
               .then()
               .statusCode(200)
               .body(notNullValue());
    }

    @Test
    @DisplayName("querySyncLogByBatchNo-有效batchNo-返回日志")
    void querySyncLogByBatchNo_validInput_returnsLogs() {
        // Arrange
        String batchNo = "testBatchNo";

        // Act & Assert
        given().pathParam("batchNo", batchNo)
               .when()
               .get("/admin/user-sync/batchNo/{batchNo}/logs")
               .then()
               .statusCode(200)
               .body(notNullValue());
    }

    @Test
    @DisplayName("更新已登录用户AAD-正常场景")
    void updateLoggedUsersAad_validInput_returnsUpdatedUsers() {
        // Arrange
        Set<String> sourceUserIds = new HashSet<>();
        var users = Instancio.ofList(User.class)
                             .size(5)
                             .withNullable(field(User::getAadId))
                             .create();
        for (User user : users) {
            userMapper.insert(user);
            sourceUserIds.add(user.getSourceUserid());
        }
        // Act & Assert
        given().queryParam("sourceUserIds", String.join(",", sourceUserIds))
               .when()
               .post("/admin/user-sync/loggedUsers/syncAadId")
               .then()
               .statusCode(200)
               .body(notNullValue());
    }

    @Test
    @DisplayName("同步指定AAD ID用户到企业微信-正常场景")
    void syncSpecificUsers_validInput_returnsSyncReport() {
        // Arrange
        List<String> aadIds = List.of("aad1", "aad2");
        String tenantId = "ww6ffc4f642bca6ae8";

        // Act & Assert
        given().queryParam("tenantId", tenantId)
               .contentType("application/json")
               .body(aadIds)
               .when()
               .post("/admin/user-sync/users/sync")
               .then()
               .statusCode(200)
               .body(notNullValue());
    }

}