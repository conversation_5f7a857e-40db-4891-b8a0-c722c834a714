package com.wshoto.user.sync.app.controller;

import com.wshoto.user.sync.app.BaseIntegrationTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.equalTo;

class HealthControllerIntegrationTest extends BaseIntegrationTest {

    @Test
    @DisplayName("healthProxy_成功请求_返回成功响应")
    void healthProxy_successRequest_returnsSuccessResponse() {
        // Arrange & Act & Assert
        given().get("/health/shallow")
               .then()
               .statusCode(200)
               .contentType("application/json")
               .body("health", equalTo(0));
    }

}