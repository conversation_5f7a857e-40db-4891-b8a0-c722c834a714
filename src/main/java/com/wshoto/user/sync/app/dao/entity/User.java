package com.wshoto.user.sync.app.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 成员同步表
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("us_user")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 成员microsoft AAD系统提供的uuid
     */
    @TableField(value = "aad_id")
    private String aadId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 成员userid
     */
    @TableField("source_userid")
    private String sourceUserid;

    /**
     * 成员名称
     */
    @TableField("name")
    private String name;

    /**
     * 成员别名
     */
    @TableField("alias")
    private String alias;

    /**
     * 手机号码
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 职务信息
     */
    @TableField("position")
    private String position;

    /**
     * 性别。1表示男性，2表示女性
     */
    @TableField("gender")
    private String gender;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 企业邮箱
     */
    @TableField("biz_mail")
    private String bizMail;

    /**
     * 座机
     */
    @TableField("telephone")
    private String telephone;

    /**
     * 直属上级
     */
    @TableField("direct_leader")
    private String directLeader;

    /**
     * 成员头像的mediaid
     */
    @TableField("avatar_mediaid")
    private String avatarMediaid;

    /**
     * 启用/禁用成员。1表示启用成员，0表示禁用成员
     */
    @TableField("enable")
    private Integer enable;

    /**
     * 自定义字段
     */
    @TableField("extattr")
    private String extattr;

    /**
     * 是否邀请该成员使用企业微信，默认值为true
     */
    @TableField("to_invite")
    private Integer toInvite;

    /**
     * 对外职务
     */
    @TableField("external_position")
    private String externalPosition;

    /**
     * 成员对外属性
     */
    @TableField("external_profile")
    private String externalProfile;

    /**
     * 地址。长度最大128个字符
     */
    @TableField("address")
    private String address;

    /**
     * 主部门
     */
    @TableField("main_department")
    private Integer mainDepartment;

    /**
     * 数据状态  1新增、2修改 、3删除
     */
    @TableField("data_status")
    private Integer dataStatus;

    /**
     * 同步状态  0初始值 1同步中 2同步成功 3同步失败
     */
    @TableField("sync_status")
    private Integer syncStatus;

    /**
     * 当前版本
     */
    @TableField("version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private Date gmtModified;

    /**
     * 企微成员userid
     */
    @TableField("target_userid")
    private String targetUserid;


}
