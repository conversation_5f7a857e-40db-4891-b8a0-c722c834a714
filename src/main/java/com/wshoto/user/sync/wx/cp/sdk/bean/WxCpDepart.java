package com.wshoto.user.sync.wx.cp.sdk.bean;

import com.wshoto.user.sync.wx.cp.sdk.util.json.WxCpGsonHolder;
import lombok.Data;

import java.io.Serializable;

/**
 * 企业微信的部门.
 *
 * <AUTHOR>
 */
@Data
public class WxCpDepart implements Serializable {
  private static final long serialVersionUID = -5028321625140879571L;

  private Long id;
  private String name;
  private String enName;
  private String[] departmentLeader;
  private Long parentId;
  private Long order;

  /**
   * From json wx cp depart.
   *
   * @param json the json
   * @return the wx cp depart
   */
  public static WxCpDepart fromJson(String json) {
    return WxCpGsonHolder.get().fromJson(json, WxCpDepart.class);
  }

  /**
   * To json string.
   *
   * @return the string
   */
  public String toJson() {
    return WxCpGsonHolder.get().toJson(this);
  }

}
