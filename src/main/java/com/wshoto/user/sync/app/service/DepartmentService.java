package com.wshoto.user.sync.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wshoto.user.sync.app.dao.entity.Department;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
public interface DepartmentService extends IService<Department> {

    /**
     * 同步企微
     *
     * @param departmentList
     * @param batchNo
     */
    void syncToWeCom(List<Department> departmentList, String batchNo);

    Department queryBySourceDeptId(String sourceDeptId);

    /**
     * 更新同步状态为执行状态
     * @param department
     */
    void updateSyncStatusProgressing(Department department);

    /**
     * 更新同步状态为完成状态
     * @param department
     * @param parentDeptId
     */
    void updateSyncStatusSuccess(Department department, String parentDeptId);

    /**
     * 更新同步状态为失败状态
     * @param department
     */
    void updateSyncStatusFail(Department department);

    /**
     * 删除部门
     * @param department
     */
    void deleteDepartmentSuccess(Department department);
}
