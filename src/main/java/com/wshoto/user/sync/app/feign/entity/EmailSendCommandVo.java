package com.wshoto.user.sync.app.feign.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 邮件接口入参Vo
 *
 * <AUTHOR>
 * @date 2024-7-24
 */
@Data
public class EmailSendCommandVo implements Serializable {

    // 发往的邮箱
    private String sendTo;

    // 发往的邮箱_批量
    private List<String> sendTos;

    // 邮件主题
    private String subject;

    // 邮件内容-文本
    private String text;

    // 是否为富文本
    private Boolean html;

    // 发送邮件的业务类型
    private String businessType;

    // 发送邮件的源服务
    private String source;

}
