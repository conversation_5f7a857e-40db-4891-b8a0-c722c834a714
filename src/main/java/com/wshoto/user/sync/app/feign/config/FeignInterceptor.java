package com.wshoto.user.sync.app.feign.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.context.annotation.Configuration;

import static com.wshoto.user.sync.app.common.config.WeComConversationFilter.CONVERSATION_ID_HEADER;
import static com.wshoto.user.sync.app.common.config.WeComConversationFilter.MDC_CONVERSATION_ID_KEY;

@Configuration
public class FeignInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        String conversationId = ThreadContext.get(MDC_CONVERSATION_ID_KEY);
        if (StringUtils.isNotEmpty(conversationId)) {
            template.header(CONVERSATION_ID_HEADER, conversationId);
        }
    }

}
