package com.wshoto.user.sync.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wshoto.user.sync.app.dao.entity.Department;
import com.wshoto.user.sync.app.dao.entity.DepartmentUser;

/**
 * <p>
 * 部门成员关系数据 服务类
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
public interface DepartmentUserService extends IService<DepartmentUser> {
    /**
     * 更新目标部门id
     * @param tenantId
     * @param sourceDeptId
     * @param targetDeptId
     */
    void updateTargetDeptId(String tenantId, String sourceDeptId, String targetDeptId);

    void deleteDepartmentUser(Department department);
}
