package com.wshoto.user.sync.app.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 部门成员关系数据
 * </p>
 *
 * <AUTHOR> @since 2024-02-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("us_department_user")
public class DepartmentUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 成员microsoft AAD系统提供的uuid
     */
    @TableField(value = "aad_id")
    private String aadId;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 来源部门id
     */
    @TableField("source_dept_id")
    private String sourceDeptId;

    /**
     * 目标系统(企微)部门id
     */
    @TableField("target_dept_id")
    private String targetDeptId;

    /**
     * 目标系统成员id
     */
    @TableField("tartget_userid")
    private String tartgetUserid;

    /**
     * 是否部门主管 1是 0否
     */
    @TableField("is_dept_leader")
    private Integer isDeptLeader;

    /**
     * 部门中的排序
     */
    @TableField("dept_order")
    private Integer deptOrder;

    /**
     * 当前版本
     */
    @TableField("version")
    private Integer version;

    /**
     * 删除标记  0未删除 1已删除
     */
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private Date gmtModified;


}
