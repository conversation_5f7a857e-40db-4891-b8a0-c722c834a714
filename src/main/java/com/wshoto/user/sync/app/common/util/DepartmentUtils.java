package com.wshoto.user.sync.app.common.util;

import com.google.common.collect.Lists;
import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.dao.entity.Department;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生成部门树的数据
 * <AUTHOR>
 */
public class DepartmentUtils {

    protected Map<String, Department> nodeMap;
    protected Map<String, List<Department>> parentMap;

    protected String rootSourceDeptId;

    public DepartmentUtils() {
        nodeMap = new HashMap<>();
        parentMap = new HashMap<>();
    }

    /**
     * 生成组织架构树
     *
     * @param nodes    部门列表
     * @return results 返回有树状结构的部门列表
     */
    public List<Department> generateTree(List<Department> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return Collections.emptyList();
        }
        
        // 使用Set来存储已处理的部门ID，避免重复处理
        Set<String> processedDeptIds = new HashSet<>();
        List<Department> departmentDTOS = new ArrayList<>();
        
        generateIdMap(nodes);
        
        // 如果根部门不存在，返回空列表
        if (rootSourceDeptId == null || !nodeMap.containsKey(rootSourceDeptId)) {
            return Collections.emptyList();
        }
        
        Queue<Department> queue = new LinkedList<>();
        Department rootDepartment = nodeMap.get(rootSourceDeptId);
        departmentDTOS.add(rootDepartment);
        processedDeptIds.add(rootDepartment.getSourceDeptId());
        queue.offer(rootDepartment);
        
        while (!queue.isEmpty()) {
            Department parent = queue.poll();
            List<Department> children = parentMap.get(parent.getSourceDeptId());
            
            if (!CollectionUtils.isEmpty(children)) {
                // 只添加未处理过的子部门
                for (Department child : children) {
                    if (!processedDeptIds.contains(child.getSourceDeptId())) {
                        departmentDTOS.add(child);
                        processedDeptIds.add(child.getSourceDeptId());
                        
                        // 只有未处理过且有子部门的部门才加入队列
                        if (parentMap.containsKey(child.getSourceDeptId())) {
                            queue.offer(child);
                        }
                    }
                }
            }
        }
        
        return departmentDTOS;
    }
    
    /**
     * 生成 id -> pid 的对应map关系
     *
     * @param nodes    部门列表
     */
    private void generateIdMap(List<Department> nodes) {
        nodeMap = nodes.stream().collect(Collectors.toMap(Department::getSourceDeptId,
                Function.identity(),(x1, x2)->x1));
        nodes =  Lists.newArrayList(nodeMap.values());
        nodes.forEach(node -> {
            if(JobConstant.ROOT_DEPT.equals(node.getTargetDeptId())){
                rootSourceDeptId = node.getSourceDeptId();
            }
            List<Department> qwDepartmentList = parentMap.get(node.getSourceDeptParentId());
            if(qwDepartmentList == null){
                qwDepartmentList = new ArrayList<>();
            }
            qwDepartmentList.add(node);
            parentMap.put(node.getSourceDeptParentId(),qwDepartmentList);
        });
    }
}
