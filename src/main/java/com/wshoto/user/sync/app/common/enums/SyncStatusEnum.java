package com.wshoto.user.sync.app.common.enums;
/**
 * 同步状态枚举
 * <AUTHOR>
 */
public enum SyncStatusEnum {
    /**
     * 初始化
     */
    INIT(0, "初始化"),
    /**
     * 同步中
     */
    PROGRESSING(1, "同步中"),
    /**
     * 同步成功
     */
    SUCCESS(2, "同步成功"),
    /**
     * 同步失败
     */
    FAIL(3, "同步失败");

    private Integer code;
    private String desc;

    SyncStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}