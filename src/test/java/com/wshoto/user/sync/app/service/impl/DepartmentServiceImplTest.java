package com.wshoto.user.sync.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.common.enums.DataStatusEnum;
import com.wshoto.user.sync.app.common.enums.SyncStatusEnum;
import com.wshoto.user.sync.app.component.QwSyncComponent;
import com.wshoto.user.sync.app.dao.entity.Department;
import com.wshoto.user.sync.app.dao.mapper.DepartmentMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.ibatis.session.Configuration;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
class DepartmentServiceImplTest {

    @Mock
    DepartmentMapper departmentMapper;

    @Mock
    QwSyncComponent qwSyncComponent;

    @InjectMocks
    DepartmentServiceImpl departmentService;

    @BeforeAll
    static void setUp() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new Configuration(), ""), Department.class);
    }

    @Test
    @DisplayName("queryBySourceDeptId - department found")
    void queryBySourceDeptId_departmentExists_returnDepartment() {
        // Arrange
        String sourceDeptId = "123";
        Department department = Instancio.create(Department.class);
        department.setSourceDeptId(sourceDeptId);
        department.setDeleted(JobConstant.DELETED_NO);
        when(departmentMapper.selectList(any())).thenReturn(List.of(department));

        // Act
        Department result = departmentService.queryBySourceDeptId(sourceDeptId);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getSourceDeptId()).isEqualTo(sourceDeptId);
    }

    @Test
    @DisplayName("queryBySourceDeptId - department not found")
    void queryBySourceDeptId_departmentNotExists_returnNull() {
        // Arrange
        String sourceDeptId = "nonexistent";

        // Act
        Department result = departmentService.queryBySourceDeptId(sourceDeptId);

        // Assert
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("syncToWeCom - handles empty department list successfully")
    void syncToWeCom_emptyList_noException() {
        // Arrange
        List<Department> departmentList = new ArrayList<>();
        String batchNo = "batch123";

        // Act & Assert
        assertThatNoException().isThrownBy(() -> departmentService.syncToWeCom(departmentList, batchNo));
    }

    @Test
    @DisplayName("syncToWeCom - handles department deletion")
    void syncToWeCom_departmentDeletion_success() {
        // Arrange
        Department department = new Department();
        department.setDataStatus(DataStatusEnum.DELETE.getCode());
        department.setSyncStatus(SyncStatusEnum.INIT.getCode());
        List<Department> departmentList = List.of(department);
        String batchNo = "batch123";

        // Act
        departmentService.syncToWeCom(departmentList, batchNo);

        // Assert
        verify(qwSyncComponent, times(1)).deleteDepartment(department, batchNo);
    }

    @Test
    @DisplayName("syncToWeCom - skips root department")
    void syncToWeCom_skipRootDepartment_noAction() {
        // Arrange
        Department department = new Department();
        department.setTargetDeptId(JobConstant.ROOT_DEPT);
        department.setSyncStatus(SyncStatusEnum.INIT.getCode());
        department.setDataStatus(DataStatusEnum.CREATE.getCode());
        List<Department> departmentList = List.of(department);
        String batchNo = "batch123";

        // Act
        departmentService.syncToWeCom(departmentList, batchNo);

        // Assert
        verify(qwSyncComponent, never()).createDepartment(any(), any());
        verify(qwSyncComponent, never()).updateDepartment(any(), any());
    }

    @Test
    @DisplayName("syncToWeCom - skips non-INIT status department")
    void syncToWeCom_skipNonInitStatus_noAction() {
        // Arrange
        Department department = new Department();
        department.setSyncStatus(SyncStatusEnum.SUCCESS.getCode());
        department.setDataStatus(DataStatusEnum.CREATE.getCode());
        List<Department> departmentList = List.of(department);
        String batchNo = "batch123";

        // Act
        departmentService.syncToWeCom(departmentList, batchNo);

        // Assert
        verify(qwSyncComponent, never()).createDepartment(any(), any());
        verify(qwSyncComponent, never()).updateDepartment(any(), any());
    }

    @Test
    @DisplayName("syncToWeCom - creates department successfully")
    void syncToWeCom_createDepartment_success() {
        // Arrange
        Department department = new Department();
        department.setSyncStatus(SyncStatusEnum.INIT.getCode());
        department.setDataStatus(DataStatusEnum.CREATE.getCode());
        List<Department> departmentList = List.of(department);
        String batchNo = "batch123";

        // Act
        departmentService.syncToWeCom(departmentList, batchNo);

        // Assert
        verify(qwSyncComponent, times(1)).createDepartment(department, batchNo);
    }

    @Test
    @DisplayName("syncToWeCom - updates department successfully")
    void syncToWeCom_updateDepartment_success() {
        // Arrange
        Department department = new Department();
        department.setSyncStatus(SyncStatusEnum.INIT.getCode());
        department.setDataStatus(DataStatusEnum.UPDATE.getCode());
        List<Department> departmentList = List.of(department);
        String batchNo = "batch123";

        // Act
        departmentService.syncToWeCom(departmentList, batchNo);

        // Assert
        verify(qwSyncComponent, times(1)).updateDepartment(department, batchNo);
    }

    @Test
    @DisplayName("updateSyncStatusProgressing - valid department sets sync status to PROGRESSING")
    void updateSyncStatusProgressing_validDepartment_syncStatusProgressing() {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setId(1L);
        department.setTenantId("Tenant123");
        department.setDeleted(JobConstant.DELETED_NO);
        department.setSyncStatus(SyncStatusEnum.INIT.getCode());

        // Act
        departmentService.updateSyncStatusProgressing(department);

        // Assert
        verify(departmentMapper, times(1)).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    @DisplayName("updateSyncStatusSuccess - valid input updates successfully")
    void updateSyncStatusSuccess_validInput_updatesSuccessfully() {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setId(1L);
        department.setTenantId("Tenant123");
        department.setTargetDeptId("Target123");
        department.setDeleted(JobConstant.DELETED_NO);
        String parentDeptId = "Parent123";

        // Act
        departmentService.updateSyncStatusSuccess(department, parentDeptId);

        // Assert
        verify(departmentMapper, times(1)).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    @DisplayName("updateSyncStatusFail - valid department sets sync status to FAIL")
    void updateSyncStatusFail_validDepartment_syncStatusFail() {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setId(1L);
        department.setTenantId("Tenant123");
        department.setDeleted(JobConstant.DELETED_NO);
        department.setSyncStatus(SyncStatusEnum.INIT.getCode());

        // Act
        departmentService.updateSyncStatusFail(department);

        // Assert
        verify(departmentMapper, times(1)).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    @DisplayName("updateSyncStatusFail - non-existent department results in no update")
    void updateSyncStatusFail_nonExistentDepartment_noUpdate() {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setId(999L);
        department.setTenantId("NonExistentTenant");
        department.setDeleted(JobConstant.DELETED_NO);
        department.setSyncStatus(SyncStatusEnum.INIT.getCode());

        // Act
        departmentService.updateSyncStatusFail(department);

        // Assert
        verify(departmentMapper, times(1)).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    @DisplayName("deleteDepartmentSuccess - valid department deletes successfully")
    void deleteDepartmentSuccess_validDepartment_deletesSuccessfully() {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setId(1L);
        department.setTenantId("Tenant123");
        department.setDeleted(JobConstant.DELETED_NO);

        // Act
        departmentService.deleteDepartmentSuccess(department);

        // Assert
        verify(departmentMapper, times(1)).update(any(LambdaUpdateWrapper.class));
    }

    @Test
    @DisplayName("deleteDepartmentSuccess - non-existent department results in no deletion")
    void deleteDepartmentSuccess_nonExistentDepartment_noDeletion() {
        // Arrange
        Department department = Instancio.create(Department.class);
        department.setId(999L);
        department.setTenantId("NonExistentTenant");
        department.setDeleted(JobConstant.DELETED_NO);

        // Act
        departmentService.deleteDepartmentSuccess(department);

        // Assert
        verify(departmentMapper, times(1)).update(any(LambdaUpdateWrapper.class));
    }

}