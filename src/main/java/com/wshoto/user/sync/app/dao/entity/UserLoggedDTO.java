package com.wshoto.user.sync.app.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 已登录成员
 *
 * <AUTHOR>
 * Created Date - 2025-3-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("us_logged_user")
public class UserLoggedDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成员microsoft AAD系统提供的uuid
     */
    @TableField(value = "aad_id")
    private String aadId;

    /**
     * 成员userid
     */
    @TableField("source_userid")
    private String sourceUserid;


    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

}
