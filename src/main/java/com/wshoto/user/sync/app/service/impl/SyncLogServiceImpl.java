package com.wshoto.user.sync.app.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.dao.entity.SyncLog;
import com.wshoto.user.sync.app.dao.mapper.SyncLogMapper;
import com.wshoto.user.sync.app.service.SyncLogService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 同步日志服务实现类.
 */
@Service
public class SyncLogServiceImpl extends ServiceImpl<SyncLogMapper, SyncLog> implements SyncLogService {

    @Resource
    private SyncLogMapper syncLogMapper;

    @Override
    public void saveLog(String tenantId, String url, String request, String response, int code, String batchNo) {
        SyncLog syncLog = new SyncLog();
        syncLog.setTenantId(tenantId);
        syncLog.setUrl(url);
        syncLog.setRequest(request);
        syncLog.setResult(response);
        syncLog.setStatus(String.valueOf(code));
        syncLog.setBatchNo(batchNo);
        syncLog.setVersion(0);
        syncLog.setDeleted(JobConstant.DELETED_NO);
        syncLog.setGmtCreate(new Date());
        syncLog.setGmtModified(new Date());
        syncLogMapper.insert(syncLog);
    }

}
