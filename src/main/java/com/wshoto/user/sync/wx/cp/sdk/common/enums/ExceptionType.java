package com.wshoto.user.sync.wx.cp.sdk.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <pre>
 * ticket类型枚举
 * 
 * </pre>
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum ExceptionType {
  /**
   * jsapi
   */
  PARENT_DEPT_NOT_FOUND(1000,"父部门未发现"),
  CREATE_DEPT_ERROR(1001,"创建部门异常"),
  UPDATE_DEPT_ERROR(1002,"修改部门异常"),
  DELETE_DEPT_ERROR(1003,"删除部门异常"),
  CREATE_USER_ERROR(1004,"创建员工异常"),
  UPDATE_USER_ERROR(1005,"修改员工异常"),
  DELETE_USER_ERROR(1006,"删除员工异常"),
  ;

  private final int code;
  /**
   * type代码
   */
  private final String desc;

}
