package com.wshoto.user.sync.wx.cp.sdk.bean;

import com.google.gson.annotations.SerializedName;
import com.wshoto.user.sync.wx.cp.sdk.util.json.WxCpGsonHolder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 获取成员ID列表返回参数
 *
 * <AUTHOR> href="https://gitee.com/<PERSON>_Wong/"><PERSON>_<PERSON></a> created on  2022/08/09
 */
@Data
public class WxCpDeptUserResult extends WxCpBaseResp {
  private static final long serialVersionUID = 1420065684270213578L;

  @SerializedName("next_cursor")
  private String nextCursor;

  @SerializedName("dept_user")
  private List<DeptUserList> deptUser;

  /**
   * The type Dept user list.
   */
  @Getter
  @Setter
  public static class DeptUserList implements Serializable {
    private static final long serialVersionUID = 1420065684270213578L;

    @SerializedName("userid")
    private String userId;

    @SerializedName("department")
    private Long department;

    /**
     * From json dept user list.
     *
     * @param json the json
     * @return the dept user list
     */
    public static DeptUserList fromJson(String json) {
      return WxCpGsonHolder.get().fromJson(json, DeptUserList.class);
    }

    /**
     * To json string.
     *
     * @return the string
     */
    public String toJson() {
      return WxCpGsonHolder.get().toJson(this);
    }

  }

  /**
   * From json wx cp dept user result.
   *
   * @param json the json
   * @return the wx cp dept user result
   */
  public static WxCpDeptUserResult fromJson(String json) {
    return WxCpGsonHolder.get().fromJson(json, WxCpDeptUserResult.class);
  }

  public String toJson() {
    return WxCpGsonHolder.get().toJson(this);
  }

}
