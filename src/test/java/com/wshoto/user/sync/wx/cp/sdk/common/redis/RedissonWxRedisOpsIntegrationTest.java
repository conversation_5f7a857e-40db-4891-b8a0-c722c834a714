package com.wshoto.user.sync.wx.cp.sdk.common.redis;

import com.wshoto.user.sync.app.BaseIntegrationTest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * RedissonWxRedisOps集成测试
 * 
 * 测试包括：
 * 1. 正常Redis操作
 * 2. Redis异常情况下的降级处理
 */
@Import(RedisTestConfig.class)
public class RedissonWxRedisOpsIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private RedissonClient redissonClient;
    
    private RedissonWxRedisOps redisOps;
    
    @Mock
    private RedissonClient mockRedissonClient;
    
    @Mock
    private RBucket<String> mockBucket;
    
    private RedissonWxRedisOps mockRedisOps;
    
    private static final String TEST_KEY_PREFIX = "test:redis:ops:";
    
    @BeforeEach
    void setUp() {
        // 使用真实的Redis客户端进行测试
        redisOps = new RedissonWxRedisOps(redissonClient);
        
        // 使用Mock的Redis客户端进行异常测试
        mockRedisOps = new RedissonWxRedisOps(mockRedissonClient);
    }
    
    @AfterEach
    void tearDown() {
        // 清理测试数据
        redissonClient.getKeys().getKeysStream()
                .filter(key -> key.startsWith(TEST_KEY_PREFIX))
                .forEach(key -> redissonClient.getKeys().delete(key));
    }
    
    @Test
    void testGetValue_Normal() {
        // 准备测试数据
        String key = TEST_KEY_PREFIX + UUID.randomUUID();
        String value = "test-value";
        redissonClient.getBucket(key).set(value);
        
        // 执行测试
        String result = redisOps.getValue(key);
        
        // 验证结果
        assertEquals(value, result);
    }
    
    @Test
    void testGetValue_KeyNotExist() {
        // 准备测试数据
        String key = TEST_KEY_PREFIX + UUID.randomUUID();
        
        // 执行测试
        String result = redisOps.getValue(key);
        
        // 验证结果
        assertNull(result);
    }
    
    @Test
    void testGetValue_Exception() {
        // 模拟异常
        when(mockRedissonClient.getBucket(anyString())).thenThrow(new RuntimeException("模拟Redis异常"));
        
        // 执行测试
        String result = mockRedisOps.getValue("any-key");
        
        // 验证结果：应该返回null而不是抛出异常
        assertNull(result);
    }
    
    @Test
    void testSetValue_Normal() {
        // 准备测试数据
        String key = TEST_KEY_PREFIX + UUID.randomUUID();
        String value = "test-value";
        
        // 执行测试
        redisOps.setValue(key, value, 60, TimeUnit.SECONDS);
        
        // 验证结果
        String result = redissonClient.getBucket(key).get().toString();
        assertEquals(value, result);
        
        // 验证过期时间设置正确（允许1秒误差）
        long ttl = redissonClient.getBucket(key).remainTimeToLive();
        assertTrue(ttl > 0 && ttl <= 60 * 1000);
    }
    
    @Test
    void testSetValue_NoExpire() {
        // 准备测试数据
        String key = TEST_KEY_PREFIX + UUID.randomUUID();
        String value = "test-value";
        
        // 执行测试：不设置过期时间
        redisOps.setValue(key, value, 0, TimeUnit.SECONDS);
        
        // 验证结果
        String result = redissonClient.getBucket(key).get().toString();
        assertEquals(value, result);
        
        // 验证没有设置过期时间
        long ttl = redissonClient.getBucket(key).remainTimeToLive();
        assertEquals(-1, ttl); // -1表示永不过期
    }

    @Test
    void testGetExpire_Normal() {
        // 准备测试数据
        String key = TEST_KEY_PREFIX + UUID.randomUUID();
        String value = "test-value";
        redissonClient.getBucket(key).set(value, 30, TimeUnit.SECONDS);
        
        // 执行测试
        Long expire = redisOps.getExpire(key);
        
        // 验证结果（允许1秒误差）
        assertTrue(expire >= 29 && expire <= 30);
    }
    
    @Test
    void testGetExpire_Exception() {
        // 模拟异常
        when(mockRedissonClient.getBucket(anyString())).thenThrow(new RuntimeException("模拟Redis异常"));
        
        // 执行测试
        Long expire = mockRedisOps.getExpire("any-key");
        
        // 验证结果：应该返回0而不是抛出异常
        assertEquals(0L, expire);
    }
    
    @Test
    void testExpire_Normal() {
        // 准备测试数据
        String key = TEST_KEY_PREFIX + UUID.randomUUID();
        String value = "test-value";
        redissonClient.getBucket(key).set(value);
        
        // 执行测试
        redisOps.expire(key, 45, TimeUnit.SECONDS);
        
        // 验证结果（允许1秒误差）
        long ttl = redissonClient.getBucket(key).remainTimeToLive();
        assertTrue(ttl > 0 && ttl <= 45 * 1000);
    }
    
    @Test
    void testExpire_Exception() {
        when(mockBucket.expire(any(Duration.class))).thenThrow(new RuntimeException("模拟Redis异常"));

        // 执行测试
        mockRedisOps.expire("any-key", 60, TimeUnit.SECONDS);

        // 验证不抛出异常，测试通过即可
    }
    
    @Test
    void testGetLock_Normal() {
        // 准备测试数据
        String key = TEST_KEY_PREFIX + UUID.randomUUID();
        
        // 执行测试
        Lock lock = redisOps.getLock(key);
        
        // 验证结果
        assertNotNull(lock);
        
        // 测试锁功能
        boolean locked = lock.tryLock();
        try {
            assertTrue(locked);
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }
    
    @Test
    void testGetLock_Exception() {
        // 模拟异常
        when(mockRedissonClient.getLock(anyString())).thenThrow(new RuntimeException("模拟Redis异常"));
        
        // 执行测试
        Lock lock = mockRedisOps.getLock("any-key");
        
        // 验证结果：应该返回本地锁而不是抛出异常
        assertNotNull(lock);
        
        // 测试锁功能
        boolean locked = lock.tryLock();
        try {
            assertTrue(locked);
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }
}
