package com.wshoto.user.sync.app.component;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wshoto.user.sync.app.common.constant.JobConstant;
import com.wshoto.user.sync.app.dao.entity.Department;
import com.wshoto.user.sync.app.dao.entity.DepartmentUser;
import com.wshoto.user.sync.app.dao.entity.User;
import com.wshoto.user.sync.app.dao.entity.UserSyncConfig;
import com.wshoto.user.sync.app.service.DepartmentService;
import com.wshoto.user.sync.app.service.DepartmentUserService;
import com.wshoto.user.sync.app.service.SyncLogService;
import com.wshoto.user.sync.app.service.UserService;
import com.wshoto.user.sync.wx.cp.builder.DepartmentBuilder;
import com.wshoto.user.sync.wx.cp.builder.UserBuilder;
import com.wshoto.user.sync.wx.cp.config.WxCpConfiguration;
import com.wshoto.user.sync.wx.cp.sdk.api.WxCpDepartmentService;
import com.wshoto.user.sync.wx.cp.sdk.api.WxCpService;
import com.wshoto.user.sync.wx.cp.sdk.api.WxCpUserService;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpDepart;
import com.wshoto.user.sync.wx.cp.sdk.bean.WxCpUser;
import com.wshoto.user.sync.wx.cp.sdk.common.enums.ExceptionType;
import com.wshoto.user.sync.wx.cp.sdk.common.error.UserSyncException;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxCpErrorMsgEnum;
import com.wshoto.user.sync.wx.cp.sdk.common.error.WxErrorException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.wshoto.user.sync.wx.cp.sdk.constant.WxCpApiPathConsts.Department.DEPARTMENT_CREATE;
import static com.wshoto.user.sync.wx.cp.sdk.constant.WxCpApiPathConsts.Department.DEPARTMENT_DELETE;
import static com.wshoto.user.sync.wx.cp.sdk.constant.WxCpApiPathConsts.Department.DEPARTMENT_UPDATE;
import static com.wshoto.user.sync.wx.cp.sdk.constant.WxCpApiPathConsts.User.USER_CREATE;
import static com.wshoto.user.sync.wx.cp.sdk.constant.WxCpApiPathConsts.User.USER_DELETE;
import static com.wshoto.user.sync.wx.cp.sdk.constant.WxCpApiPathConsts.User.USER_UPDATE;

/**
 * 企微接口同步部门成员
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class QwSyncComponent {

    @Resource
    private SyncLogService syncLogService;

    @Resource
    @Lazy
    private DepartmentService departmentService;

    @Resource
    @Lazy
    private UserService userService;

    @Resource
    @Lazy
    private DepartmentUserService departmentUserService;

    @Resource
    private WxCpConfiguration wxCpConfiguration;

    public void createDepartment(Department department, String batchNo) {
        WxCpDepart build = DepartmentBuilder.build(department);
        try {
            log.info("批次: {}, 开始创建部门, 部门名称: {}, 源部门ID: {}", batchNo, department.getName(),
                     department.getSourceDeptId());
            departmentService.updateSyncStatusProgressing(department);
            WxCpDepartmentService wxCpDepartmentService = getWxCpDepartmentService(department.getTenantId());
            if (ObjectUtil.isEmpty(build.getParentId())) {
                String sourceDeptParentId = department.getSourceDeptParentId();
                Department parentDept = departmentService.queryBySourceDeptId(sourceDeptParentId);
                if (!ObjectUtil.isEmpty(parentDept) && StrUtil.isNotBlank(parentDept.getTargetDeptId())) {
                    build.setParentId(Long.parseLong(parentDept.getTargetDeptId()));
                    log.info("批次: {}, 部门 {} 找到父部门, 父部门ID: {}", batchNo, department.getName(),
                             build.getParentId());
                } else {
                    log.warn("批次: {}, 部门 {} 未找到父部门, 源父部门ID: {}", batchNo, department.getName(),
                             sourceDeptParentId);
                    throw new UserSyncException(ExceptionType.PARENT_DEPT_NOT_FOUND);
                }
            } else {
                build.setParentId(1L);
                log.info("批次: {}, 部门 {} 使用默认父部门ID: 1", batchNo, department.getName());
            }
            Long deptId = wxCpDepartmentService.create(build);
            department.setTargetDeptId(deptId.toString());
            departmentService.updateSyncStatusSuccess(department, build.getParentId()
                                                                       .toString());
            departmentUserService.updateTargetDeptId(department.getTenantId(), department.getSourceDeptId(),
                                                     department.getTargetDeptId());
            log.info("批次: {}, 部门创建成功, 部门名称: {}, 目标部门ID: {}, 父部门ID: {}", batchNo,
                     department.getName(), deptId, build.getParentId());
            syncLogService.saveLog(department.getTenantId(), DEPARTMENT_CREATE, JSONUtil.toJsonStr(build),
                                   deptId.toString(), WxCpErrorMsgEnum.CODE_0.getCode(), batchNo);
        } catch (WxErrorException e) {
            log.error("批次: {}, 部门创建失败(微信错误), 部门名称: {}, 错误码: {}, 错误信息: {}", batchNo,
                      department.getName(), e.getError()
                                             .getErrorCode(), e.getError()
                                                               .getErrorMsg());
            departmentService.updateSyncStatusFail(department);
            syncLogService.saveLog(department.getTenantId(), DEPARTMENT_CREATE, JSONUtil.toJsonStr(build),
                                   JSONUtil.toJsonStr(e.getError()), e.getError()
                                                                      .getErrorCode(), batchNo);
        } catch (UserSyncException e) {
            log.error("批次: {}, 部门创建失败(同步错误), 部门名称: {}, 错误码: {}, 错误信息: {}", batchNo,
                      department.getName(), e.getCode(), e.getMessage());
            departmentService.updateSyncStatusFail(department);
            syncLogService.saveLog(department.getTenantId(), DEPARTMENT_CREATE, JSONUtil.toJsonStr(build),
                                   JSONUtil.toJsonStr(e.getMessage()), e.getCode(), batchNo);
        } catch (Exception e) {
            log.error("批次: {}, 部门创建失败(系统异常), 部门名称: {}, 异常信息: {}", batchNo, department.getName(),
                      e.getMessage(), e);
            departmentService.updateSyncStatusFail(department);
            syncLogService.saveLog(department.getTenantId(), DEPARTMENT_CREATE, JSONUtil.toJsonStr(build),
                                   JSONUtil.toJsonStr(e.getMessage()), ExceptionType.CREATE_DEPT_ERROR.getCode(),
                                   batchNo);
        }
    }

    public void updateDepartment(Department department, String batchNo) {
        WxCpDepart build = DepartmentBuilder.build(department);
        try {
            log.info("批次: {}, 开始更新部门, 部门名称: {}, 目标部门ID: {}", batchNo, department.getName(),
                     department.getTargetDeptId());
            departmentService.updateSyncStatusProgressing(department);
            //查询上级部门
            Department parent = departmentService.getOne(
                    new LambdaQueryWrapper<Department>().eq(Department::getTenantId, department.getTenantId())
                                                        .eq(Department::getDeleted, JobConstant.DELETED_NO)
                                                        .eq(Department::getSourceDeptId,
                                                            department.getSourceDeptParentId())
                                                        .last("limit 1"));
            if (Objects.nonNull(parent) && Objects.nonNull(parent.getTargetDeptId()) && !parent.getTargetDeptId()
                                                                                               .equals(department.getTargetDeptParentId())) {
                build.setParentId(Long.valueOf(parent.getTargetDeptId()));
                log.info("批次: {}, 部门 {} 父部门变更, 新父部门ID: {}, 旧父部门ID: {}", batchNo, department.getName(),
                         parent.getTargetDeptId(), department.getTargetDeptParentId());
            }
            WxCpDepartmentService wxCpDepartmentService = getWxCpDepartmentService(department.getTenantId());
            wxCpDepartmentService.update(build);
            departmentService.updateSyncStatusSuccess(department, build.getParentId()
                                                                       .toString());
            departmentUserService.updateTargetDeptId(department.getTenantId(), department.getSourceDeptId(),
                                                     department.getTargetDeptId());
            log.info("批次: {}, 部门更新成功, 部门名称: {}, 目标部门ID: {}", batchNo, department.getName(),
                     department.getTargetDeptId());
            syncLogService.saveLog(department.getTenantId(), DEPARTMENT_UPDATE, JSONUtil.toJsonStr(build), null,
                                   WxCpErrorMsgEnum.CODE_0.getCode(), batchNo);
        } catch (WxErrorException e) {
            log.error("批次: {}, 部门更新失败(微信错误), 部门名称: {}, 目标部门ID: {}, 错误码: {}, 错误信息: {}",
                      batchNo, department.getName(), department.getTargetDeptId(), e.getError()
                                                                                    .getErrorCode(), e.getError()
                                                                                                      .getErrorMsg());
            departmentService.updateSyncStatusFail(department);
            syncLogService.saveLog(department.getTenantId(), DEPARTMENT_UPDATE, JSONUtil.toJsonStr(build),
                                   JSONUtil.toJsonStr(e.getError()), e.getError()
                                                                      .getErrorCode(), batchNo);
        } catch (Exception e) {
            log.error("批次: {}, 部门更新失败(系统异常), 部门名称: {}, 目标部门ID: {}, 异常信息: {}", batchNo,
                      department.getName(), department.getTargetDeptId(), e.getMessage(), e);
            departmentService.updateSyncStatusFail(department);
            syncLogService.saveLog(department.getTenantId(), DEPARTMENT_UPDATE, JSONUtil.toJsonStr(build),
                                   JSONUtil.toJsonStr(e.getMessage()), ExceptionType.UPDATE_DEPT_ERROR.getCode(),
                                   batchNo);
        }
    }

    public void deleteDepartment(Department department, String batchNo) {
        try {
            log.info("批次: {}, 开始删除部门, 部门名称: {}, 目标部门ID: {}", batchNo, department.getName(),
                     department.getTargetDeptId());
            departmentService.updateSyncStatusProgressing(department);
            WxCpDepartmentService wxCpDepartmentService = getWxCpDepartmentService(department.getTenantId());
            wxCpDepartmentService.delete(Long.valueOf(department.getTargetDeptId()));
            departmentService.deleteDepartmentSuccess(department);
            departmentUserService.deleteDepartmentUser(department);
            log.info("批次: {}, 部门删除成功, 部门名称: {}, 目标部门ID: {}", batchNo, department.getName(),
                     department.getTargetDeptId());
            syncLogService.saveLog(department.getTenantId(), DEPARTMENT_DELETE, department.getTargetDeptId(), null,
                                   WxCpErrorMsgEnum.CODE_0.getCode(), batchNo);
        } catch (WxErrorException e) {
            log.error("批次: {}, 部门删除失败(微信错误), 部门名称: {}, 目标部门ID: {}, 错误码: {}, 错误信息: {}",
                      batchNo, department.getName(), department.getTargetDeptId(), e.getError()
                                                                                    .getErrorCode(), e.getError()
                                                                                                      .getErrorMsg());
            departmentService.updateSyncStatusFail(department);
            syncLogService.saveLog(department.getTenantId(), DEPARTMENT_DELETE, department.getTargetDeptId(),
                                   JSONUtil.toJsonStr(e.getError()), e.getError()
                                                                      .getErrorCode(), batchNo);
        } catch (Exception e) {
            log.error("批次: {}, 部门删除失败(系统异常), 部门名称: {}, 目标部门ID: {}, 异常信息: {}", batchNo,
                      department.getName(), department.getTargetDeptId(), e.getMessage(), e);
            departmentService.updateSyncStatusFail(department);
            syncLogService.saveLog(department.getTenantId(), DEPARTMENT_DELETE, JSONUtil.toJsonStr(department),
                                   JSONUtil.toJsonStr(e.getMessage()), ExceptionType.DELETE_DEPT_ERROR.getCode(),
                                   batchNo);
        }
    }

    public void createUser(User user, List<DepartmentUser> departmentUserList, String batchNo,
                           Map<String, UserSyncConfig> createConfigMap, Map<String, Method> userField) {
        WxCpUser wxCpUser = new WxCpUser();
        try {
            log.info("批次: {}, 开始创建用户, 用户名: {}, AadId: {}", batchNo, user.getName(), user.getAadId());
            userService.updateSyncStatusProgressing(user);
            wxCpUser = UserBuilder.build(user, departmentUserList, createConfigMap, userField);
            WxCpUserService wxCpUserService = getWxCpUserService(user.getTenantId());
            wxCpUserService.create(wxCpUser);
            userService.updateSyncStatusSuccess(user);
            log.info("批次: {}, 用户创建成功, 用户名: {}, AadId: {}, 目标用户ID: {}, 部门关系数: {}", batchNo,
                     user.getName(), user.getAadId(), user.getTargetUserid(), departmentUserList.size());
            syncLogService.saveLog(user.getTenantId(), USER_CREATE, JSONUtil.toJsonStr(wxCpUser), null,
                                   WxCpErrorMsgEnum.CODE_0.getCode(), batchNo);
        } catch (WxErrorException e) {
            log.error("批次: {}, 用户创建失败(微信错误), 用户名: {}, AadId: {}, 错误码: {}, 错误信息: {}", batchNo,
                      user.getName(), user.getAadId(), e.getError()
                                                        .getErrorCode(), e.getError()
                                                                          .getErrorMsg());
            userService.updateSyncStatusFail(user);
            syncLogService.saveLog(user.getTenantId(), USER_CREATE, JSONUtil.toJsonStr(wxCpUser),
                                   JSONUtil.toJsonStr(e.getError()), e.getError()
                                                                      .getErrorCode(), batchNo);
            throw new RuntimeException("企业微信API调用失败: " + e.getError()
                                                                  .getErrorCode() + " - " + e.getError()
                                                                                             .getErrorMsg(), e);
        } catch (Exception e) {
            log.error("批次: {}, 用户创建失败(系统异常), 用户名: {}, AadId: {}, 异常信息: {}", batchNo, user.getName(),
                      user.getAadId(), e.getMessage(), e);
            userService.updateSyncStatusFail(user);
            syncLogService.saveLog(user.getTenantId(), USER_CREATE, JSONUtil.toJsonStr(user),
                                   JSONUtil.toJsonStr(e.getMessage()), ExceptionType.CREATE_USER_ERROR.getCode(),
                                   batchNo);
        }
    }

    public void updateUser(User user, List<DepartmentUser> departmentUserList, String batchNo,
                           Map<String, UserSyncConfig> updateConfigMap, Map<String, Method> userField) {
        WxCpUser wxCpUser = new WxCpUser();
        try {
            log.info("批次: {}, 开始更新用户, 用户名: {}, AadId: {}, 目标用户ID: {}", batchNo, user.getName(),
                     user.getAadId(), user.getTargetUserid());
            userService.updateSyncStatusProgressing(user);
            wxCpUser = UserBuilder.build(user, departmentUserList, updateConfigMap, userField);
            WxCpUserService wxCpUserService = getWxCpUserService(user.getTenantId());
            wxCpUserService.update(wxCpUser);
            userService.updateSyncStatusSuccess(user);
            log.info("批次: {}, 用户更新成功, 用户名: {}, AadId: {}, 目标用户ID: {}, 部门关系数: {}", batchNo,
                     user.getName(), user.getAadId(), user.getTargetUserid(), departmentUserList.size());
            syncLogService.saveLog(user.getTenantId(), USER_UPDATE, JSONUtil.toJsonStr(wxCpUser), null,
                                   WxCpErrorMsgEnum.CODE_0.getCode(), batchNo);
        } catch (WxErrorException e) {
            log.error(
                    "批次: {}, 用户更新失败(微信错误), 用户名: {}, AadId: {}, 目标用户ID: {}, 错误码: {}, 错误信息: {}",
                    batchNo, user.getName(), user.getAadId(), user.getTargetUserid(), e.getError()
                                                                                       .getErrorCode(), e.getError()
                                                                                                         .getErrorMsg());
            userService.updateSyncStatusFail(user);
            syncLogService.saveLog(user.getTenantId(), USER_UPDATE, JSONUtil.toJsonStr(wxCpUser),
                                   JSONUtil.toJsonStr(e.getError()), e.getError()
                                                                      .getErrorCode(), batchNo);
            throw new RuntimeException("企业微信API调用失败: " + e.getError()
                                                                  .getErrorCode() + " - " + e.getError()
                                                                                             .getErrorMsg(), e);
        } catch (Exception e) {
            log.error("批次: {}, 用户更新失败(系统异常), 用户名: {}, AadId: {}, 目标用户ID: {}, 异常信息: {}", batchNo,
                      user.getName(), user.getAadId(), user.getTargetUserid(), e.getMessage(), e);
            userService.updateSyncStatusFail(user);
            syncLogService.saveLog(user.getTenantId(), USER_UPDATE, JSONUtil.toJsonStr(user),
                                   JSONUtil.toJsonStr(e.getMessage()), ExceptionType.UPDATE_USER_ERROR.getCode(),
                                   batchNo);
        }
    }

    public void deleteUser(User user, List<DepartmentUser> departmentUserList1, String batchNo) {
        try {
            log.info("批次: {}, 开始删除用户, 用户名: {}, AadId: {}, 目标用户ID: {}", batchNo, user.getName(),
                     user.getAadId(), user.getTargetUserid());
            userService.updateSyncStatusProgressing(user);
            WxCpUserService wxCpUserService = getWxCpUserService(user.getTenantId());
            wxCpUserService.delete(user.getAadId());
            userService.deleteUserSuccess(user);
            departmentUserService.update(
                    new LambdaUpdateWrapper<DepartmentUser>().eq(DepartmentUser::getTenantId, user.getTenantId())
                                                             .eq(DepartmentUser::getDeleted, JobConstant.DELETED_NO)
                                                             .eq(DepartmentUser::getTartgetUserid,
                                                                 user.getTargetUserid())
                                                             .eq(DepartmentUser::getAadId, user.getAadId())
                                                             .set(DepartmentUser::getDeleted, JobConstant.DELETED_YES)
                                                             .set(DepartmentUser::getGmtModified, new Date()));
            log.info("批次: {}, 用户删除成功, 用户名: {}, AadId: {}, 目标用户ID: {}", batchNo, user.getName(),
                     user.getAadId(), user.getTargetUserid());
            syncLogService.saveLog(user.getTenantId(), USER_DELETE, user.getTargetUserid(), null,
                                   WxCpErrorMsgEnum.CODE_0.getCode(), batchNo);
        } catch (WxErrorException e) {
            log.error(
                    "批次: {}, 用户删除失败(微信错误), 用户名: {}, AadId: {}, 目标用户ID: {}, 错误码: {}, 错误信息: {}",
                    batchNo, user.getName(), user.getAadId(), user.getTargetUserid(), e.getError()
                                                                                       .getErrorCode(), e.getError()
                                                                                                         .getErrorMsg());
            userService.updateSyncStatusFail(user);
            syncLogService.saveLog(user.getTenantId(), USER_DELETE, user.getTargetUserid(),
                                   JSONUtil.toJsonStr(e.getError()), e.getError()
                                                                      .getErrorCode(), batchNo);
            throw new RuntimeException("企业微信API调用失败: " + e.getError()
                                                                  .getErrorCode() + " - " + e.getError()
                                                                                             .getErrorMsg(), e);
        } catch (Exception e) {
            log.error("批次: {}, 用户删除失败(系统异常), 用户名: {}, AadId: {}, 目标用户ID: {}, 异常信息: {}", batchNo,
                      user.getName(), user.getAadId(), user.getTargetUserid(), e.getMessage(), e);
            userService.updateSyncStatusFail(user);
            syncLogService.saveLog(user.getTenantId(), USER_DELETE, JSONUtil.toJsonStr(user),
                                   JSONUtil.toJsonStr(e.getMessage()), ExceptionType.DELETE_USER_ERROR.getCode(),
                                   batchNo);
        }
    }

    /**
     * 对于1200+已登录员工，使用旧UPN作为企微id进行删除
     */
    public void deleteUserOldManner(User user, String batchNo) {
        try {
            log.info("批次: {}, 开始删除已登录用户(旧方式), 用户信息: {}", batchNo, JSONUtil.toJsonStr(user));
            userService.updateSyncStatusProgressing(user);
            WxCpUserService wxCpUserService = getWxCpUserService(user.getTenantId());
            wxCpUserService.delete(user.getTargetUserid());
            userService.deleteUserSuccess(user);
            departmentUserService.update(
                    new LambdaUpdateWrapper<DepartmentUser>().eq(DepartmentUser::getTenantId, user.getTenantId())
                                                             .eq(DepartmentUser::getDeleted, JobConstant.DELETED_NO)
                                                             .eq(DepartmentUser::getTartgetUserid,
                                                                 user.getTargetUserid())
                                                             .eq(DepartmentUser::getAadId, user.getAadId())
                                                             .set(DepartmentUser::getDeleted, JobConstant.DELETED_YES)
                                                             .set(DepartmentUser::getGmtModified, new Date()));
            log.info("批次: {}, 已登录用户删除成功(旧方式), 用户信息: {}", batchNo, JSONUtil.toJsonStr(user));
            syncLogService.saveLog(user.getTenantId(), USER_DELETE, user.getTargetUserid(), null,
                                   WxCpErrorMsgEnum.CODE_0.getCode(), batchNo);
        } catch (WxErrorException e) {
            log.error("批次: {}, 已登录用户删除失败(旧方式)(微信错误), 用户信息: {}", batchNo, JSONUtil.toJsonStr(user),
                      e.getError()
                       .getErrorCode(), e.getError()
                                         .getErrorMsg());
            userService.updateSyncStatusFail(user);
            syncLogService.saveLog(user.getTenantId(), USER_DELETE, user.getTargetUserid(),
                                   JSONUtil.toJsonStr(e.getError()), e.getError()
                                                                      .getErrorCode(), batchNo);
            throw new RuntimeException("企业微信API调用失败(旧方式): " + e.getError()
                                                                          .getErrorCode() + " - " + e.getError()
                                                                                                     .getErrorMsg(), e);
        } catch (Exception e) {
            log.error("批次: {}, 已登录用户删除失败(旧方式)(系统异常), 用户信息: {}", batchNo, JSONUtil.toJsonStr(user),
                      e.getMessage(), e);
            userService.updateSyncStatusFail(user);
            syncLogService.saveLog(user.getTenantId(), USER_DELETE, JSONUtil.toJsonStr(user),
                                   JSONUtil.toJsonStr(e.getMessage()), ExceptionType.DELETE_USER_ERROR.getCode(),
                                   batchNo);
        }
    }

    private WxCpDepartmentService getWxCpDepartmentService(String tenantId) {
        WxCpService cpService = wxCpConfiguration.getCpService(tenantId);
        return cpService.getDepartmentService();
    }

    private WxCpUserService getWxCpUserService(String tenantId) {
        WxCpService cpService = wxCpConfiguration.getCpService(tenantId);
        return cpService.getUserService();
    }

}
